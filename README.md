# TestPlatform

一个基于 Go 的微服务测试平台，使用 gRPC 和 PostgreSQL。

## 项目结构

```
TestPlatform/
├── cmd/                    # 应用程序入口点
│   ├── api-gateway/       # API 网关服务
│   ├── test-service/      # 测试服务
│   ├── user-service/      # 用户服务
│   └── report-service/    # 报告服务
├── internal/              # 内部包，不对外暴露
│   ├── api-gateway/       # API 网关内部逻辑
│   ├── test-service/      # 测试服务内部逻辑
│   ├── user-service/      # 用户服务内部逻辑
│   ├── report-service/    # 报告服务内部逻辑
│   └── shared/           # 共享组件
├── pkg/                   # 可对外暴露的包
│   ├── auth/             # 认证相关
│   ├── config/           # 配置管理
│   ├── database/         # 数据库连接
│   ├── logger/           # 日志组件
│   └── utils/            # 工具函数
├── api/                   # API 定义
│   └── proto/            # protobuf 文件
├── migrations/            # 数据库迁移文件
├── configs/              # 配置文件
├── scripts/              # 脚本文件
├── deployments/          # 部署相关文件
├── docs/                 # 文档
└── tests/                # 测试文件
```

## 技术栈

- **语言**: Go 1.21+
- **框架**: Gin (HTTP), gRPC
- **数据库**: PostgreSQL
- **ORM**: GORM
- **配置管理**: Viper
- **日志**: Logrus
- **API 网关**: grpc-gateway

## 快速开始

1. 克隆项目
2. 安装依赖: `go mod tidy`
3. 配置数据库连接
4. 运行服务: `make run`

## 服务说明

- **API Gateway**: 统一入口，HTTP 到 gRPC 的转换
- **User Service**: 用户管理服务
- **Test Service**: 测试用例管理和执行服务
- **Report Service**: 测试报告生成和管理服务
