# Go 参数
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod

# 二进制文件名
BINARY_NAME_GATEWAY=api-gateway
BINARY_NAME_USER=user-service
BINARY_NAME_TEST=test-service
BINARY_NAME_REPORT=report-service

# 构建目录
BUILD_DIR=build

.PHONY: all build clean test deps proto run-gateway run-user run-test run-report

# 默认目标
all: clean deps proto build

# 安装依赖
deps:
	$(GOMOD) download
	$(GOMOD) tidy

# 生成 protobuf 文件
proto:
	@echo "Generating protobuf files..."
	protoc --go_out=. --go_opt=paths=source_relative \
		--go-grpc_out=. --go-grpc_opt=paths=source_relative \
		api/proto/user/user.proto
	protoc --go_out=. --go_opt=paths=source_relative \
		--go-grpc_out=. --go-grpc_opt=paths=source_relative \
		api/proto/test/test.proto
	protoc --go_out=. --go_opt=paths=source_relative \
		--go-grpc_out=. --go-grpc_opt=paths=source_relative \
		api/proto/report/report.proto

# 构建所有服务
build: build-gateway build-user build-test build-report

# 构建 API 网关
build-gateway:
	$(GOBUILD) -o $(BUILD_DIR)/$(BINARY_NAME_GATEWAY) ./cmd/api-gateway

# 构建用户服务
build-user:
	$(GOBUILD) -o $(BUILD_DIR)/$(BINARY_NAME_USER) ./cmd/user-service

# 构建测试服务
build-test:
	$(GOBUILD) -o $(BUILD_DIR)/$(BINARY_NAME_TEST) ./cmd/test-service

# 构建报告服务
build-report:
	$(GOBUILD) -o $(BUILD_DIR)/$(BINARY_NAME_REPORT) ./cmd/report-service

# 清理
clean:
	$(GOCLEAN)
	rm -rf $(BUILD_DIR)

# 运行测试
test:
	$(GOTEST) -v ./...

# 运行 API 网关
run-gateway:
	$(GOCMD) run ./cmd/api-gateway

# 运行用户服务
run-user:
	$(GOCMD) run ./cmd/user-service

# 运行测试服务
run-test:
	$(GOCMD) run ./cmd/test-service

# 运行报告服务
run-report:
	$(GOCMD) run ./cmd/report-service

# 运行所有服务（后台）
run-all:
	$(GOCMD) run ./cmd/user-service &
	$(GOCMD) run ./cmd/test-service &
	$(GOCMD) run ./cmd/report-service &
	$(GOCMD) run ./cmd/api-gateway

# Docker 相关
docker-build:
	docker-compose build

docker-up:
	docker-compose up -d

docker-down:
	docker-compose down

# 数据库迁移
migrate-up:
	migrate -path migrations -database "postgres://postgres:password@localhost:5432/testplatform?sslmode=disable" up

migrate-down:
	migrate -path migrations -database "postgres://postgres:password@localhost:5432/testplatform?sslmode=disable" down

# 格式化代码
fmt:
	$(GOCMD) fmt ./...

# 代码检查
lint:
	golangci-lint run

# 生成文档
docs:
	swag init -g cmd/api-gateway/main.go
