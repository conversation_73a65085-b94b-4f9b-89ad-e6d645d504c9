package client

import (
	"context"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	US "thinta.test.platform/api/proto/user"
	"thinta.test.platform/internal/shared/models"
	"thinta.test.platform/pkg/config"
	"thinta.test.platform/pkg/database"
	"thinta.test.platform/pkg/model"
)

var LocalUser *models.LocalUser

type UserServiceClient struct {
	client US.UserServiceClient
}

func NewUserServiceClient(address string, cfg config.Database) (*UserServiceClient, error) {
	conn, err := grpc.NewClient(address, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		return nil, err
	}

	err = database.NewSQLiteDB(cfg)
	if err != nil {
		return nil, err
	}

	return &UserServiceClient{client: US.NewUserServiceClient(conn)}, nil
}

// 获取服务信息
// 该方法用于获取服务信息，返回服务名称、版本、时间和描述
func (c *UserServiceClient) GetServiceInfo() (*US.GetServiceInfoResponse, error) {
	return c.client.GetServiceInfo(context.Background(), &US.GetServiceInfoRequest{})
}

// 注册
func (c *UserServiceClient) Register(username, email, fullName string) (*US.RegisterResponse, error) {
	return c.client.Register(context.Background(), &US.RegisterRequest{
		Username: username,
		Email:    email,
		FullName: fullName,
	})
}

// 登录
func (c *UserServiceClient) Login(username, password string) (*US.LoginResponse, error) {
	res, err := c.client.Login(context.Background(), &US.LoginRequest{
		Username: username,
		Password: password,
	})
	if err != nil {
		return nil, err
	}
	LocalUser = &models.LocalUser{
		Token:    res.Token,
		Username: res.User.Username,
		Email:    res.User.Email,
		FullName: res.User.FullName,
		Role:     res.User.Role,
	}

	// 更新本地缓存的用户数据
	if err := model.UpdateLocalUser(LocalUser); err != nil {
		return nil, err
	}

	return res, nil
}

// 登出
func (c *UserServiceClient) Logout(token string) (*US.LogoutResponse, error) {
	res, err := c.client.Logout(context.Background(), &US.LogoutRequest{
		Token: token,
	})
	if err != nil {
		return nil, err
	}
	// 清空本地缓存的用户数据
	if err := model.DeleteLocalUser(); err != nil {
		return nil, err
	}
	return res, nil
}

// 获取用户列表
func (c *UserServiceClient) GetUsers(page, pageSize int32, search string) (*US.GetUsersResponse, error) {
	return c.client.GetUsers(context.Background(), &US.GetUsersRequest{
		Page:     page,
		PageSize: pageSize,
		Search:   search,
	})
}

// 更新用户
func (c *UserServiceClient) UpdateUser(id int64, username, email, fullName string) (*US.UpdateUserResponse, error) {
	return c.client.UpdateUser(context.Background(), &US.UpdateUserRequest{
		Id:       id,
		Username: username,
		Email:    email,
		FullName: fullName,
	})
}

// 修改密码
func (c *UserServiceClient) ChangeUserPassword(username, oldPassword, newPassword string) (*US.ChangeUserPasswordResponse, error) {
	return c.client.ChangeUserPassword(context.Background(), &US.ChangeUserPasswordRequest{
		Username:    username,
		OldPassword: oldPassword,
		NewPassword: newPassword,
	})
}

// 修改用户角色
func (c *UserServiceClient) ChangeUserRole(id int64, username string, role int32) (*US.ChangeUserRoleResponse, error) {
	return c.client.ChangeUserRole(context.Background(), &US.ChangeUserRoleRequest{
		Id:       id,
		Username: username,
		Role:     role,
	})
}

// 修改用户状态
func (c *UserServiceClient) ChangeUserStatus(id int64, username string, status int32) (*US.ChangeUserStatusResponse, error) {
	return c.client.ChangeUserStatus(context.Background(), &US.ChangeUserStatusRequest{
		Id:       id,
		Username: username,
		Status:   status,
	})
}

// 修改用户测试模块
func (c *UserServiceClient) ChangeUserTestModules(id int64, username string, testModules []string) (*US.ChangeUserTestModulesResponse, error) {
	return c.client.ChangeUserTestModules(context.Background(), &US.ChangeUserTestModulesRequest{
		Id:          id,
		Username:    username,
		TestModules: testModules,
	})
}

// 删除用户
func (c *UserServiceClient) DeleteUser(username string) (*US.DeleteUserResponse, error) {
	return c.client.DeleteUser(context.Background(), &US.DeleteUserRequest{
		Username: username,
	})
}

// 用户认证
func (c *UserServiceClient) AuthenticateUser(password string) (*US.AuthenticateUserResponse, error) {
	return c.client.AuthenticateUser(context.Background(), &US.AuthenticateUserRequest{
		Password: password,
	})
}

// 用户密码重置
func (c *UserServiceClient) ResetUserPassword(requestId int64, username string) (*US.ResetUserPasswordResponse, error) {
	return c.client.ResetUserPassword(context.Background(), &US.ResetUserPasswordRequest{
		RequestId: requestId,
		Username:  username,
	})
}
