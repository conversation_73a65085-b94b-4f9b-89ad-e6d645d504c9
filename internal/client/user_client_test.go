package client

import (
	"testing"
	"time"

	"thinta.test.platform/pkg/config"
	"thinta.test.platform/pkg/database"
)

func TestUserServiceClient(t *testing.T) {
	// 启动 gprc 用户服务
	go func() {
		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Logf("Failed to load config: %v", err)
			return
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Logf("Failed to create database connection: %v", err)
			return
		}

		// 启动服务
		err = StartUserService("localhost:8080", database.PlatformDB)
		if err != nil {
			t.Logf("Failed to start service: %v", err)
			return
		}
	}()

	// 等待服务启动
	t.Log("Waiting for service to start...")
	var client *UserServiceClient
	for i := 0; i < 10; i++ {
		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Logf("Failed to load config: %v", err)
			return
		}

		// 创建客户端
		c, err := NewUserServiceClient("localhost:8080", cfg.Database)
		if err == nil {
			client = c
			break
		}
		t.Logf("Failed to create client: %v", err)
		t.Log("Retrying...")
		time.Sleep(1 * time.Second)
	}

	if client == nil {
		t.Fatal("Failed to create client")
		return
	}

	// 1. 获取测试服务信息
	resp, err := client.GetServiceInfo()
	if err != nil {
		t.Errorf("GetServiceInfo failed: %v", err)
		return
	}
	t.Logf("GetServiceInfo success: %v", resp)

	// 2. 用户注册
	now := time.Now()
	userTime := now.Format("200601021504")
	username := "C-" + userTime
	email := "CEmail" + userTime + "@test.com"
	fullName := "TUser" + userTime
	resp2, err := client.Register(username, email, fullName)
	if err != nil {
		t.Errorf("Register failed: %v", err)
		return
	}
	t.Logf("Register success: %v", resp2)

	// 3. 用户登录（预期失败）
	_, err = client.Login(username, "11111111")
	if err == nil {
		t.Errorf("Login success, but expected failed")
		return
	}
	t.Logf("Login failed as expected: %v", err)

	// 4. 管理员登录
	resp4, err := client.Login("admin", "admin")
	if err != nil {
		t.Errorf("Admin login failed: %v", err)
		return
	}
	t.Logf("Admin login success: %v", resp4)
	// adminToken := resp4.Token
	// adminMD := metadata.Pairs("authorization", adminToken)
	// adminCtx := metadata.NewIncomingContext(context.Background(), adminMD)

	// 5. 获取用户列表
	resp5, err := client.GetUsers(1, 10, "")
	if err != nil {
		t.Errorf("GetUsers failed: %v", err)
		return
	}
	t.Logf("GetUsers success: %v", resp5)

	// 6. 修改用户状态
	resp6, err := client.ChangeUserStatus(resp5.Users[0].Id, username, 1)
	if err != nil {
		t.Errorf("ChangeUserStatus failed: %v", err)
		return
	}
	t.Logf("ChangeUserStatus success: %v", resp6)

	// 7. 修改用户角色
	resp7, err := client.ChangeUserRole(resp5.Users[0].Id, username, 2)
	if err != nil {
		t.Errorf("ChangeUserRole failed: %v", err)
		return
	}
	t.Logf("ChangeUserRole success: %v", resp7)

	// 8. 修改用户密码
	resp8, err := client.ChangeUserPassword(username, "11111111", "12345678")
	if err != nil {
		t.Errorf("ChangeUserPassword failed: %v", err)
		return
	}
	t.Logf("ChangeUserPassword success: %v", resp8)

	// 9. 用户登录（新注册用户）
	resp9, err := client.Login(username, "12345678")
	if err != nil {
		t.Errorf("Login failed: %v", err)
		return
	}
	t.Logf("Login success: %v", resp9)

	// 10. 修改用户密码（新注册用户）
	resp10, err := client.ChangeUserPassword(username, "12345678", "11111111")
	if err != nil {
		t.Errorf("ChangeUserPassword failed: %v", err)
		return
	}
	t.Logf("ChangeUserPassword success: %v", resp10)

	// 11. 用户登出
	resp11, err := client.Logout(resp9.Token)
	if err != nil {
		t.Errorf("Logout failed: %v", err)
		return
	}
	t.Logf("Logout success: %v", resp11)
}
