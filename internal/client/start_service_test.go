package client

import (
	"testing"
	"time"

	"thinta.test.platform/pkg/config"
	"thinta.test.platform/pkg/database"
)

func TestStartUserService(t *testing.T) {
	// 加载配置
	cfg, err := config.Load("../../configs")
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	// 创建数据库连接
	err = database.NewPlatformDB(cfg.Database)
	if err != nil {
		t.Fatalf("Failed to create database connection: %v", err)
	}

	// 在后台启动服务
	go func() {
		err := StartUserService("localhost:8081", database.PlatformDB)
		if err != nil {
			t.Logf("Failed to start service: %v", err)
		}
	}()

	// 等待服务启动
	time.Sleep(2 * time.Second)

	// 尝试创建客户端连接
	client, err := NewUserServiceClient("localhost:8081", config.Database{
		LocalPath: "./.local/",
	})
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}

	// 测试服务信息获取
	resp, err := client.GetServiceInfo()
	if err != nil {
		t.Errorf("GetServiceInfo failed: %v", err)
		return
	}

	t.Logf("GetServiceInfo success: %v", resp)
}
