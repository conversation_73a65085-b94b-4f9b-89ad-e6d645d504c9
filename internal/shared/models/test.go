package models

import (
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// TestStatus 测试状态
type TestStatus string

const (
	TestStatusPending TestStatus = "pending"
	TestStatusRunning TestStatus = "running"
	TestStatusPassed  TestStatus = "passed"
	TestStatusFailed  TestStatus = "failed"
	TestStatusSkipped TestStatus = "skipped"
)

type NestedMap map[string]map[string]string

func (m *NestedMap) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	b, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("expected []byte, got %T", value)
	}
	return json.Unmarshal(b, m)
}

// TestModule 测试模块模型
type TestModule struct {
	ID      int64  `gorm:"primaryKey;autoIncrement" json:"id"`
	Name    string `gorm:"uniqueIndex;not null;size:128" json:"name"`
	Plugin  string `gorm:"uniqueIndex;not null;size:20" json:"plugin"`
	Version string `gorm:"not null;size:20" json:"version"`
	// Items       map[string]map[string]string `gorm:"type:jsonb" json:"items"`
	Items       NestedMap      `gorm:"type:jsonb" json:"items"`
	Description string         `gorm:"type:text" json:"description"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

// TestModuleApply 测试模块申请模型
// Approved: 0 - pending, 1 - approved, 2 - rejected
type TestModuleApply struct {
	ID        int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	RequestBy int64     `gorm:"not null" json:"request_by"`
	Modules   []string  `gorm:"-" json:"modules"`
	Approved  int32     `gorm:"default:0;not null" json:"approved"`
	CreatedAt time.Time `json:"created_at"`

	// 关联
	Requestor User `gorm:"foreignKey:RequestBy" json:"requestor,omitempty"`
}

// Test 测试记录模型
type TestRecord struct {
	ID           int64          `gorm:"primaryKey;autoIncrement" json:"id"`
	RecordNumber string         `gorm:"uniqueIndex;not null;size:200" json:"record_number"`
	Description  string         `gorm:"type:text" json:"description"`
	Module       string         `gorm:"not null;size:20" json:"modbule"`
	Result       string         `gorm:"type:text" json:"result"`
	CreatedBy    int64          `gorm:"not null" json:"created_by"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`

	// 关联
	Creator    User            `gorm:"foreignKey:CreatedBy" json:"creator,omitempty"`
	TestModule TestModule      `gorm:"foreignKey:Module;references:Name" json:"test_module,omitempty"`
	Executions []TestExecution `gorm:"foreignKey:TestID" json:"executions,omitempty"`
}

// TestExecution 测试执行记录模型
type TestExecution struct {
	ID           int64      `gorm:"primaryKey;autoIncrement" json:"id"`
	TestID       int64      `gorm:"not null;index" json:"test_id"`
	Status       TestStatus `gorm:"not null;size:20" json:"status"`
	Result       string     `gorm:"type:text" json:"result"`
	ErrorMessage string     `gorm:"type:text" json:"error_message"`
	DurationMs   int64      `gorm:"default:0" json:"duration_ms"`
	ExecutedBy   int64      `gorm:"not null" json:"executed_by"`
	Environment  string     `gorm:"size:50" json:"environment"`
	StartedAt    time.Time  `json:"started_at"`
	FinishedAt   time.Time  `json:"finished_at"`
	CreatedAt    time.Time  `json:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at"`

	// 关联
	Test     TestRecord `gorm:"foreignKey:TestID" json:"test,omitempty"`
	Executor User       `gorm:"foreignKey:ExecutedBy" json:"executor,omitempty"`
}

// TableName 指定表名
func (TestModule) TableName() string {
	return "test_modules"
}

func (TestModuleApply) TableName() string {
	return "test_module_requests"
}

// TableName 指定表名
func (TestRecord) TableName() string {
	return "test_records"
}

// TableName 指定表名
func (TestExecution) TableName() string {
	return "test_executions"
}
