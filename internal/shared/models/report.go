package models

import (
	"time"

	"gorm.io/gorm"
)

// ReportType 报告类型
type ReportType string

const (
	ReportTypeTestExecution ReportType = "test_execution"
	ReportTypePerformance   ReportType = "performance"
	ReportTypeCoverage      ReportType = "coverage"
	ReportTypeSummary       ReportType = "summary"
)

// ReportFormat 报告格式
type ReportFormat string

const (
	ReportFormatHTML ReportFormat = "html"
	ReportFormatPDF  ReportFormat = "pdf"
	ReportFormatJSON ReportFormat = "json"
	ReportFormatXML  ReportFormat = "xml"
)

// Report 报告模型
type Report struct {
	ID          int64          `gorm:"primaryKey;autoIncrement" json:"id"`
	Title       string         `gorm:"not null;size:200" json:"title"`
	Description string         `gorm:"type:text" json:"description"`
	Type        ReportType     `gorm:"not null;size:20" json:"type"`
	Format      ReportFormat   `gorm:"not null;size:10" json:"format"`
	FilePath    string         `gorm:"size:500" json:"file_path"`
	Content     string         `gorm:"type:text" json:"content"`
	CreatedBy   int64          `gorm:"not null" json:"created_by"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`

	// 关联
	Creator User `gorm:"foreignKey:CreatedBy" json:"creator,omitempty"`
}

// TableName 指定表名
func (Report) TableName() string {
	return "reports"
}

// TestStatistics 测试统计信息
type TestStatistics struct {
	TotalTests         int64   `json:"total_tests"`
	PassedTests        int64   `json:"passed_tests"`
	FailedTests        int64   `json:"failed_tests"`
	SkippedTests       int64   `json:"skipped_tests"`
	PassRate           float64 `json:"pass_rate"`
	TotalDurationMs    int64   `json:"total_duration_ms"`
	AverageDurationMs  int64   `json:"average_duration_ms"`
}
