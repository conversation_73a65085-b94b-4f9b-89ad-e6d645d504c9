package models

import (
	"time"

	"gorm.io/gorm"
)

// User 用户模型
// Role: 0 - visitor, 1 - admin, 2 - developer, 3 - tester, 4 - record manager, 5 - info manager
// Status: 0 - unaudited, 1 - active, 2 - freeze, 3 - deleted
// TestModules: 当前用户可使用的测试模块（即可使用哪些测试系统）
type User struct {
	ID          int64          `gorm:"primaryKey;autoIncrement" json:"id"`
	Username    string         `gorm:"uniqueIndex;not null;size:50" json:"username"`
	Email       string         `gorm:"uniqueIndex;not null;size:100" json:"email"`
	Password    string         `gorm:"not null;size:255" json:"password"`
	FullName    string         `gorm:"size:100" json:"full_name"`
	Role        int32          `gorm:"not null;size:20;default:0" json:"role"`
	Status      int32          `gorm:"default:0" json:"status"`
	TestModules []string       `gorm:"-" json:"test_modules"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// BeforeCreate 创建前钩子
func (u *User) BeforeCreate(tx *gorm.DB) error {
	// 这里可以添加密码加密等逻辑
	return nil
}

type LocalUser struct {
	ID          int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	Token       string    `gorm:"uniqueIndex;not null;size:255" json:"token"`
	Username    string    `gorm:"uniqueIndex;not null;size:50" json:"username"`
	Email       string    `gorm:"uniqueIndex;not null;size:100" json:"email"`
	FullName    string    `gorm:"size:100" json:"full_name"`
	Role        int32     `gorm:"not null;size:20;default:0" json:"role"`
	TestModules []string  `gorm:"-" json:"test_modules"`
	CreatedAt   time.Time `json:"created_at"`
}

func (LocalUser) TableName() string {
	return "local_user"
}

func (u *LocalUser) BeforeCreate(tx *gorm.DB) error {
	// 这里可以添加密码加密等逻辑
	return nil
}
