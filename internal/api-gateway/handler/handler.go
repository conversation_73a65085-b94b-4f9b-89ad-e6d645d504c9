package handler

import (
	"github.com/gin-gonic/gin"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	"thinta.test.platform/pkg/config"
	"thinta.test.platform/pkg/logger"
)

// Handler API 网关处理器
type Handler struct {
	cfg *config.Config
}

// NewHandler 创建处理器实例
func NewHandler(cfg *config.Config) *Handler {
	h := &Handler{
		cfg: cfg,
	}

	// 初始化 gRPC 客户端连接
	h.initGRPCClients()

	return h
}

// initGRPCClients 初始化 gRPC 客户端
func (h *Handler) initGRPCClients() {
	// 用户服务客户端
	_, err := grpc.Dial("localhost:"+h.cfg.UserService.Port, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		logger.Fatalf("Failed to connect to user service: %v", err)
	}
	// h.userClient = userPb.NewUserServiceClient(userConn)

	// 测试服务客户端
	_, err = grpc.Dial("localhost:"+h.cfg.TestService.Port, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		logger.Fatalf("Failed to connect to test service: %v", err)
	}
	// h.testClient = testPb.NewTestServiceClient(testConn)

	// 报告服务客户端
	_, err = grpc.Dial("localhost:"+h.cfg.ReportService.Port, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		logger.Fatalf("Failed to connect to report service: %v", err)
	}
	// h.reportClient = reportPb.NewReportServiceClient(reportConn)
}

// CreateUser 创建用户
func (h *Handler) CreateUser(c *gin.Context) {
	// var req userPb.CreateUserRequest
	// if err := c.ShouldBindJSON(&req); err != nil {
	// 	utils.BadRequestResponse(c, "Invalid request body")
	// 	return
	// }

	// ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	// defer cancel()

	// resp, err := h.userClient.CreateUser(ctx, &req)
	// if err != nil {
	// 	logger.WithField("error", err).Error("Failed to create user")
	// 	utils.InternalServerErrorResponse(c, "Failed to create user")
	// 	return
	// }

	// utils.SuccessResponse(c, resp.User)
}

// GetUser 获取用户
func (h *Handler) GetUser(c *gin.Context) {
	// idStr := c.Param("id")
	// id, err := strconv.ParseInt(idStr, 10, 64)
	// if err != nil {
	// 	utils.BadRequestResponse(c, "Invalid user ID")
	// 	return
	// }

	// ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	// defer cancel()

	// resp, err := h.userClient.GetUser(ctx, &userPb.GetUserRequest{Id: id})
	// if err != nil {
	// 	logger.WithField("error", err).Error("Failed to get user")
	// 	utils.InternalServerErrorResponse(c, "Failed to get user")
	// 	return
	// }

	// utils.SuccessResponse(c, resp.User)
}

// UpdateUser 更新用户
func (h *Handler) UpdateUser(c *gin.Context) {
	// idStr := c.Param("id")
	// id, err := strconv.ParseInt(idStr, 10, 64)
	// if err != nil {
	// 	utils.BadRequestResponse(c, "Invalid user ID")
	// 	return
	// }

	// var req userPb.UpdateUserRequest
	// if err := c.ShouldBindJSON(&req); err != nil {
	// 	utils.BadRequestResponse(c, "Invalid request body")
	// 	return
	// }
	// req.Id = id

	// ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	// defer cancel()

	// resp, err := h.userClient.UpdateUser(ctx, &req)
	// if err != nil {
	// 	logger.WithField("error", err).Error("Failed to update user")
	// 	utils.InternalServerErrorResponse(c, "Failed to update user")
	// 	return
	// }

	// utils.SuccessResponse(c, resp.User)
}

// DeleteUser 删除用户
func (h *Handler) DeleteUser(c *gin.Context) {
	// idStr := c.Param("id")
	// id, err := strconv.ParseInt(idStr, 10, 64)
	// if err != nil {
	// 	utils.BadRequestResponse(c, "Invalid user ID")
	// 	return
	// }

	// ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	// defer cancel()

	// resp, err := h.userClient.DeleteUser(ctx, &userPb.DeleteUserRequest{Id: id})
	// if err != nil {
	// 	logger.WithField("error", err).Error("Failed to delete user")
	// 	utils.InternalServerErrorResponse(c, "Failed to delete user")
	// 	return
	// }

	// utils.SuccessResponse(c, gin.H{"success": resp.Success})
}

// CreateTest 创建测试
func (h *Handler) CreateTest(c *gin.Context) {
	// var req testPb.CreateTestRequest
	// if err := c.ShouldBindJSON(&req); err != nil {
	// 	utils.BadRequestResponse(c, "Invalid request body")
	// 	return
	// }

	// ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	// defer cancel()

	// resp, err := h.testClient.CreateTest(ctx, &req)
	// if err != nil {
	// 	logger.WithField("error", err).Error("Failed to create test")
	// 	utils.InternalServerErrorResponse(c, "Failed to create test")
	// 	return
	// }

	// utils.SuccessResponse(c, resp.Test)
}

// GetTest 获取测试
func (h *Handler) GetTest(c *gin.Context) {
	// idStr := c.Param("id")
	// id, err := strconv.ParseInt(idStr, 10, 64)
	// if err != nil {
	// 	utils.BadRequestResponse(c, "Invalid test ID")
	// 	return
	// }

	// ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	// defer cancel()

	// resp, err := h.testClient.GetTest(ctx, &testPb.GetTestRequest{Id: id})
	// if err != nil {
	// 	logger.WithField("error", err).Error("Failed to get test")
	// 	utils.InternalServerErrorResponse(c, "Failed to get test")
	// 	return
	// }

	// utils.SuccessResponse(c, resp.Test)
}

// RunTest 执行测试
func (h *Handler) RunTest(c *gin.Context) {
	// idStr := c.Param("id")
	// testId, err := strconv.ParseInt(idStr, 10, 64)
	// if err != nil {
	// 	utils.BadRequestResponse(c, "Invalid test ID")
	// 	return
	// }

	// var req testPb.RunTestRequest
	// if err := c.ShouldBindJSON(&req); err != nil {
	// 	utils.BadRequestResponse(c, "Invalid request body")
	// 	return
	// }
	// req.TestId = testId

	// ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	// defer cancel()

	// resp, err := h.testClient.RunTest(ctx, &req)
	// if err != nil {
	// 	logger.WithField("error", err).Error("Failed to run test")
	// 	utils.InternalServerErrorResponse(c, "Failed to run test")
	// 	return
	// }

	// utils.SuccessResponse(c, resp.Execution)
}

// ListReports 报告列表
func (h *Handler) ListReports(c *gin.Context) {
	// page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	// pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	// search := c.Query("search")

	// ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	// defer cancel()

	// resp, err := h.reportClient.ListReports(ctx, &reportPb.ListReportsRequest{
	// 	Page:     int32(page),
	// 	PageSize: int32(pageSize),
	// 	Search:   search,
	// })
	// if err != nil {
	// 	logger.WithField("error", err).Error("Failed to list reports")
	// 	utils.InternalServerErrorResponse(c, "Failed to list reports")
	// 	return
	// }

	// utils.SuccessResponse(c, gin.H{
	// 	"reports":   resp.Reports,
	// 	"total":     resp.Total,
	// 	"page":      resp.Page,
	// 	"page_size": resp.PageSize,
	// })
}

// GetReport 获取报告
func (h *Handler) GetReport(c *gin.Context) {
	// idStr := c.Param("id")
	// id, err := strconv.ParseInt(idStr, 10, 64)
	// if err != nil {
	// 	utils.BadRequestResponse(c, "Invalid report ID")
	// 	return
	// }

	// ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	// defer cancel()

	// resp, err := h.reportClient.GetReport(ctx, &reportPb.GetReportRequest{Id: id})
	// if err != nil {
	// 	logger.WithField("error", err).Error("Failed to get report")
	// 	utils.InternalServerErrorResponse(c, "Failed to get report")
	// 	return
	// }

	// utils.SuccessResponse(c, resp.Report)
}
