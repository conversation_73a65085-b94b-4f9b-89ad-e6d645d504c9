package server

import (
	"context"
	"testing"

	"thinta.test.platform/api/proto/test"
	"thinta.test.platform/pkg/config"
	"thinta.test.platform/pkg/database"
)

func TestGetTestServiceInfo(t *testing.T) {
	t.Run("GetServiceInfo", func(t *testing.T) {
		// 创建请求
		req := &test.GetServiceInfoRequest{}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewTestServer(database.PlatformDB)
		// 调用 GetServiceInfo 方法
		resp, err := s.GetServiceInfo(context.Background(), req)
		if err != nil {
			t.<PERSON><PERSON><PERSON>("GetServiceInfo failed: %v", err)
		}

		// 检查响应
		if resp.Name == "" {
			t.<PERSON>rror("Expected name in response")
		} else {
			t.Logf("GetServiceInfo success: %v", resp)
		}
	})
}

func TestCreateTestModule(t *testing.T) {
	t.Run("CreateTestModule", func(t *testing.T) {
		// 创建请求
		req := &test.CreateTestModuleRequest{
			Name:        "Module-0001",
			Plugin:      "Module_0001.so",
			Version:     "1.0.0",
			Description: "测试版本测试模块（系统）01",
			Items: map[string]*test.KeyValuePair{
				"测试块1": {
					Values: map[string]string{
						"item01": "测试项1",
						"item02": "测试项2",
						"item03": "测试项3",
						"item04": "测试项4",
						"item05": "测试项5",
					},
				},
				"测试块2": {
					Values: map[string]string{
						"item01": "测试项1",
						"item02": "测试项2",
						"item03": "测试项3",
					},
				},
			},
		}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewTestServer(database.PlatformDB)
		// 调用 CreateTestModule 方法
		resp, err := s.CreateTestModule(context.Background(), req)
		if err != nil {
			t.Errorf("CreateTestModule failed: %v", err)
		}

		// 检查响应
		if resp.TestModule == nil {
			t.Error("Expected test module in response")
		} else {
			t.Logf("CreateTestModule success: %v", resp.TestModule)
		}
	})
}

func TestGetTestModule(t *testing.T) {
	t.Run("GetTestModule", func(t *testing.T) {
		// 创建请求
		req := &test.GetTestModuleRequest{
			Name: "Module-0001",
		}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewTestServer(database.PlatformDB)
		// 调用 GetTestModule 方法
		resp, err := s.GetTestModule(context.Background(), req)
		if err != nil {
			t.Errorf("GetTestModule failed: %v", err)
		}

		// 检查响应
		if resp.TestModule == nil {
			t.Error("Expected test module in response")
		} else {
			t.Logf("GetTestModule success: %v", resp.TestModule)
		}
	})
}

func TestUpdateTestModule(t *testing.T) {
	t.Run("UpdateTestModule", func(t *testing.T) {
		// 创建请求
		req := &test.UpdateTestModuleRequest{
			Name:        "Module-0001",
			Plugin:      "Module-0001.so",
			Version:     "1.1.0",
			Description: "测试版本测试模块（系统）01-20250606",
		}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewTestServer(database.PlatformDB)
		// 调用 UpdateTestModule 方法
		resp, err := s.UpdateTestModule(context.Background(), req)
		if err != nil {
			t.Errorf("UpdateTestModule failed: %v", err)
		}

		// 检查响应
		if resp.TestModule == nil {
			t.Error("Expected test module in response")
		} else {
			t.Logf("UpdateTestModule success: %v", resp.TestModule)
		}
	})
}

func TestDeleteTestModule(t *testing.T) {
	t.Run("DeleteTestModule", func(t *testing.T) {
		// 创建请求
		req := &test.DeleteTestModuleRequest{
			Name: "Module-0001",
		}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewTestServer(database.PlatformDB)
		// 调用 DeleteTestModule 方法
		resp, err := s.DeleteTestModule(context.Background(), req)
		if err != nil {
			t.Errorf("DeleteTestModule failed: %v", err)
		}

		// 检查响应
		if !resp.Success {
			t.Error("Expected success in response")
		} else {
			t.Logf("DeleteTestModule success: %v", resp.Success)
		}
	})
}

func TestListTestModules(t *testing.T) {
	t.Run("ListTestModules", func(t *testing.T) {
		// 创建请求
		req := &test.ListTestModulesRequest{
			Page:     1,
			PageSize: 10,
			Search:   "",
		}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewTestServer(database.PlatformDB)
		// 调用 ListTestModules 方法
		resp, err := s.ListTestModules(context.Background(), req)
		if err != nil {
			t.Errorf("ListTestModules failed: %v", err)
		}

		// 检查响应
		if len(resp.TestModules) == 0 {
			t.Error("Expected test modules in response")
		} else {
			t.Logf("ListTestModules success: %v", resp.TestModules)
		}
	})
}

func TestDownloadTestModule(t *testing.T) {
	t.Run("DownloadTestModule", func(t *testing.T) {
		// 创建请求
		req := &test.DownloadTestModuleRequest{
			Name: "Module-0001",
		}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewTestServer(database.PlatformDB)
		// 调用 DownloadTestModule 方法
		resp, err := s.DownloadTestModule(context.Background(), req)
		if err != nil {
			t.Errorf("DownloadTestModule failed: %v", err)
		}

		// 检查响应
		if len(resp.Content) == 0 {
			t.Error("Expected content in response")
		} else {
			t.Logf("DownloadTestModule success: %v", resp.Content)
		}
	})
}

func TestUploadTestModule(t *testing.T) {
	t.Run("UploadTestModule", func(t *testing.T) {
		// 创建请求
		req := &test.UploadTestModuleRequest{
			Name:        "test",
			Plugin:      "test",
			Version:     "1.0.0",
			Description: "test",
			Content:     []byte("test"),
		}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewTestServer(database.PlatformDB)
		// 调用 UploadTestModule 方法
		resp, err := s.UploadTestModule(context.Background(), req)
		if err != nil {
			t.Errorf("UploadTestModule failed: %v", err)
		}

		// 检查响应
		if resp.TestModule == nil {
			t.Error("Expected test module in response")
		} else {
			t.Logf("UploadTestModule success: %v", resp.TestModule)
		}
	})
}

func TestApplyTestModule(t *testing.T) {
	t.Run("ApplyTestModule", func(t *testing.T) {
		// 创建请求
		req := &test.ApplyTestModuleRequest{
			Id:        1,
			RequestBy: 1,
			Modules:   []string{"test"},
		}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewTestServer(database.PlatformDB)
		// 调用 ApplyTestModule 方法
		resp, err := s.ApplyTestModule(context.Background(), req)
		if err != nil {
			t.Errorf("ApplyTestModule failed: %v", err)
		}

		// 检查响应
		if resp.TestModuleApply == nil {
			t.Error("Expected test module apply in response")
		} else {
			t.Logf("ApplyTestModule success: %v", resp.TestModuleApply)
		}
	})
}

func TestListTestModuleApplies(t *testing.T) {
	t.Run("ListTestModuleApplies", func(t *testing.T) {
		// 创建请求
		req := &test.ListTestModuleAppliesRequest{
			Page:     1,
			PageSize: 10,
			Search:   "",
		}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewTestServer(database.PlatformDB)
		// 调用 ListTestModuleApplies 方法
		resp, err := s.ListTestModuleApplies(context.Background(), req)
		if err != nil {
			t.Errorf("ListTestModuleApplies failed: %v", err)
		}

		// 检查响应
		if len(resp.TestModuleApplies) == 0 {
			t.Error("Expected test module applies in response")
		} else {
			t.Logf("ListTestModuleApplies success: %v", resp.TestModuleApplies)
		}
	})
}

func TestProcessTestModuleApply(t *testing.T) {
	t.Run("ProcessTestModuleApply", func(t *testing.T) {
		// 创建请求
		req := &test.ProcessTestModuleApplyRequest{
			Id:       1,
			Approved: 1,
		}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewTestServer(database.PlatformDB)
		// 调用 ProcessTestModuleApply 方法
		resp, err := s.ProcessTestModuleApply(context.Background(), req)
		if err != nil {
			t.Errorf("ProcessTestModuleApply failed: %v", err)
		}

		// 检查响应
		if !resp.Success {
			t.Error("Expected success in response")
		} else {
			t.Logf("ProcessTestModuleApply success: %v", resp.Success)
		}
	})
}

func TestCreateTestRecord(t *testing.T) {
	t.Run("CreateTestRecord", func(t *testing.T) {
		// 创建请求
		req := &test.CreateTestRecordRequest{
			RecordNumber: "test",
			Description:  "test",
			Module:       "test",
			CreatedBy:    1,
		}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewTestServer(database.PlatformDB)
		// 调用 CreateTestRecord 方法
		resp, err := s.CreateTestRecord(context.Background(), req)
		if err != nil {
			t.Errorf("CreateTestRecord failed: %v", err)
		}

		// 检查响应
		if resp.TestRecord == nil {
			t.Error("Expected test record in response")
		} else {
			t.Logf("CreateTestRecord success: %v", resp.TestRecord)
		}
	})
}

func TestGetTestRecord(t *testing.T) {
	t.Run("GetTestRecord", func(t *testing.T) {
		// 创建请求
		req := &test.GetTestRecordRequest{
			RecordNumber: "test",
		}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewTestServer(database.PlatformDB)
		// 调用 GetTestRecord 方法
		resp, err := s.GetTestRecord(context.Background(), req)
		if err != nil {
			t.Errorf("GetTestRecord failed: %v", err)
		}

		// 检查响应
		if resp.TestRecord == nil {
			t.Error("Expected test record in response")
		} else {
			t.Logf("GetTestRecord success: %v", resp.TestRecord)
		}
	})
}

func TestDeleteTestRecord(t *testing.T) {
	t.Run("DeleteTestRecord", func(t *testing.T) {
		// 创建请求
		req := &test.DeleteTestRecordRequest{
			RecordNumber: "test",
		}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewTestServer(database.PlatformDB)
		// 调用 DeleteTestRecord 方法
		resp, err := s.DeleteTestRecord(context.Background(), req)
		if err != nil {
			t.Errorf("DeleteTestRecord failed: %v", err)
		}

		// 检查响应
		if !resp.Success {
			t.Error("Expected success in response")
		} else {
			t.Logf("DeleteTestRecord success: %v", resp.Success)
		}
	})
}

func TestListTestRecords(t *testing.T) {
	t.Run("ListTestRecords", func(t *testing.T) {
		// 创建请求
		req := &test.ListTestRecordsRequest{
			Page:     1,
			PageSize: 10,
			Search:   "",
		}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewTestServer(database.PlatformDB)
		// 调用 ListTestRecords 方法
		resp, err := s.ListTestRecords(context.Background(), req)
		if err != nil {
			t.Errorf("ListTestRecords failed: %v", err)
		}

		// 检查响应
		if len(resp.TestRecords) == 0 {
			t.Error("Expected test records in response")
		} else {
			t.Logf("ListTestRecords success: %v", resp.TestRecords)
		}
	})
}

func TestCreateTestExecution(t *testing.T) {
	t.Run("CreateTestExecution", func(t *testing.T) {
		// 创建请求
		req := &test.CreateTestExecutionRequest{
			TestId:       1,
			Status:       test.TestStatus_PENDING,
			Result:       "test",
			ErrorMessage: "test",
			DurationMs:   1000,
			ExecutedBy:   1,
			Environment:  "test",
		}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewTestServer(database.PlatformDB)
		// 调用 CreateTestExecution 方法
		resp, err := s.CreateTestExecution(context.Background(), req)
		if err != nil {
			t.Errorf("CreateTestExecution failed: %v", err)
		}

		// 检查响应
		if resp.TestExecution == nil {
			t.Error("Expected test execution in response")
		} else {
			t.Logf("CreateTestExecution success: %v", resp.TestExecution)
		}
	})
}

func TestGetTestExecution(t *testing.T) {
	t.Run("GetTestExecution", func(t *testing.T) {
		// 创建请求
		req := &test.GetTestExecutionRequest{
			Id: 1,
		}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewTestServer(database.PlatformDB)
		// 调用 GetTestExecution 方法
		resp, err := s.GetTestExecution(context.Background(), req)
		if err != nil {
			t.Errorf("GetTestExecution failed: %v", err)
		}

		// 检查响应
		if resp.TestExecution == nil {
			t.Error("Expected test execution in response")
		} else {
			t.Logf("GetTestExecution success: %v", resp.TestExecution)
		}
	})
}

func TestListTestExecutions(t *testing.T) {
	t.Run("ListTestExecutions", func(t *testing.T) {
		// 创建请求
		req := &test.ListTestExecutionsRequest{
			Page:     1,
			PageSize: 10,
			Search:   "",
		}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewTestServer(database.PlatformDB)
		// 调用 ListTestExecutions 方法
		resp, err := s.ListTestExecutions(context.Background(), req)
		if err != nil {
			t.Errorf("ListTestExecutions failed: %v", err)
		}

		// 检查响应
		if len(resp.TestExecutions) == 0 {
			t.Error("Expected test executions in response")
		} else {
			t.Logf("ListTestExecutions success: %v", resp.TestExecutions)
		}
	})
}
