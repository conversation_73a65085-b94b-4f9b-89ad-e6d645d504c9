package server

import (
	"context"
	"fmt"
	"os"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"
	TS "thinta.test.platform/api/proto/test"
	"thinta.test.platform/internal/shared/models"
	"thinta.test.platform/pkg/logger"
)

type TestServer struct {
	TS.UnimplementedTestServiceServer
	db *gorm.DB
}

func NewTestServer(db *gorm.DB) *TestServer {
	return &TestServer{db: db}
}

func (s *TestServer) GetServiceInfo(ctx context.Context, req *TS.GetServiceInfoRequest) (*TS.GetServiceInfoResponse, error) {
	return &TS.GetServiceInfoResponse{
		Name:        "test-service",
		Version:     "1.0.0",
		Time:        "2023-01-01 00:00:00.000",
		Description: "振中测试平台-测试管理服务系统",
	}, nil
}

func (s *TestServer) CreateTestModule(ctx context.Context, req *TS.CreateTestModuleRequest) (*TS.CreateTestModuleResponse, error) {
	logger.WithFields(map[string]interface{}{
		"name":        req.Name,
		"plugin":      req.Plugin,
		"version":     req.Version,
		"description": req.Description,
	}).Info("Creating TestModule")

	testModule := &models.TestModule{
		Name:        req.Name,
		Plugin:      req.Plugin,
		Version:     req.Version,
		Description: req.Description,
		Items:       s.itemsPairToMap(req.Items),
	}

	if err := s.db.Create(testModule).Error; err != nil {
		logger.WithField("error", err).Error("Failed to create test module")
		return nil, status.Errorf(codes.Internal, "Failed to create test module: %v", err)
	}

	return &TS.CreateTestModuleResponse{
		TestModule: s.modelToProto(testModule),
	}, nil
}

func (s *TestServer) GetTestModule(ctx context.Context, req *TS.GetTestModuleRequest) (*TS.GetTestModuleResponse, error) {
	logger.WithField("name", req.Name).Info("Getting TestModule")

	testModule := &models.TestModule{}
	if err := s.db.First(testModule, models.TestModule{Name: req.Name}).Error; err != nil {
		logger.WithField("error", err).Error("Failed to get test module")
		return nil, status.Errorf(codes.Internal, "Failed to get test module: %v", err)
	}

	return &TS.GetTestModuleResponse{
		TestModule: s.modelToProto(testModule),
	}, nil
}

func (s *TestServer) UpdateTestModule(ctx context.Context, req *TS.UpdateTestModuleRequest) (*TS.UpdateTestModuleResponse, error) {
	logger.WithFields(map[string]interface{}{
		"name": req.Name,
	}).Info("Updating TestModule")

	testModule := &models.TestModule{}
	if err := s.db.First(testModule, models.TestModule{Name: req.Name}).Error; err != nil {
		logger.WithField("error", err).Error("Failed to get test module")
		return nil, status.Errorf(codes.Internal, "Failed to get test module: %v", err)
	}

	if req.Plugin != "" {
		testModule.Plugin = req.Plugin
	}
	if req.Version != "" {
		testModule.Version = req.Version
	}
	if req.Description != "" {
		testModule.Description = req.Description
	}
	if req.Items != nil {
		testModule.Items = s.itemsPairToMap(req.Items)
	}

	if err := s.db.Save(testModule).Error; err != nil {
		logger.WithField("error", err).Error("Failed to update test module")
		return nil, status.Errorf(codes.Internal, "Failed to update test module: %v", err)
	}

	return &TS.UpdateTestModuleResponse{
		TestModule: s.modelToProto(testModule),
	}, nil
}

func (s *TestServer) DeleteTestModule(ctx context.Context, req *TS.DeleteTestModuleRequest) (*TS.DeleteTestModuleResponse, error) {
	logger.WithField("Name", req.Name).Info("Deleting TestModule")

	testModule := &models.TestModule{}
	if err := s.db.First(testModule, models.TestModule{Name: req.Name}).Error; err != nil {
		logger.WithField("error", err).Error("Failed to get test module")
		return nil, status.Errorf(codes.Internal, "Failed to get test module: %v", err)
	}

	if err := s.db.Unscoped().Delete(testModule).Error; err != nil {
		logger.WithField("error", err).Error("Failed to delete test module")
		return nil, status.Errorf(codes.Internal, "Failed to delete test module: %v", err)
	}

	return &TS.DeleteTestModuleResponse{
		Success: true,
	}, nil
}

func (s *TestServer) ListTestModules(ctx context.Context, req *TS.ListTestModulesRequest) (*TS.ListTestModulesResponse, error) {
	logger.WithFields(map[string]interface{}{
		"page":      req.Page,
		"page_size": req.PageSize,
		"search":    req.Search,
	}).Info("Listing TestModules")

	var testModules []*models.TestModule
	if err := s.db.Limit(int(req.PageSize)).Offset(int((req.Page - 1) * req.PageSize)).Find(&testModules).Error; err != nil {
		logger.WithField("error", err).Error("Failed to list test modules")
		return nil, status.Errorf(codes.Internal, "Failed to list test modules: %v", err)
	}

	// 查询总数
	var total int64
	if err := s.db.Model(&models.TestModule{}).Count(&total).Error; err != nil {
		logger.WithField("error", err).Error("Failed to count test modules")
		return nil, status.Errorf(codes.Internal, "Failed to count test modules: %v", err)
	}

	return &TS.ListTestModulesResponse{
		TestModules: s.modelsToProtos(testModules),
		Total:       total,
		Page:        req.Page,
		PageSize:    req.PageSize,
	}, nil
}

func (s *TestServer) DownloadTestModule(ctx context.Context, req *TS.DownloadTestModuleRequest) (*TS.DownloadTestModuleResponse, error) {
	logger.WithField("name", req.Name).Info("Downloading TestModule")

	testModule := &models.TestModule{}
	if err := s.db.First(testModule, models.TestModule{Name: req.Name}).Error; err != nil {
		logger.WithField("error", err).Error("Failed to get test module")
		return nil, status.Errorf(codes.Internal, "Failed to get test module: %v", err)
	}

	plugin := testModule.Plugin
	// 检索项目目录下的 modules 目录，是否有 plugin 文件
	modulePath := fmt.Sprintf("modules/%s", plugin)
	if _, err := os.Stat(modulePath); os.IsNotExist(err) {
		logger.WithField("error", err).Error("Plugin file not found")
		return nil, status.Errorf(codes.NotFound, "Plugin file not found: %v", err)
	}
	// 如果有，则读取文件内容，返回
	file, err := os.Open(modulePath)
	if err != nil {
		logger.WithField("error", err).Error("Failed to open plugin file")
		return nil, status.Errorf(codes.Internal, "Failed to open plugin file: %v", err)
	}
	defer file.Close()

	// 读取文件内容
	fileInfo, err := file.Stat()
	if err != nil {
		logger.WithField("error", err).Error("Failed to get file info")
		return nil, status.Errorf(codes.Internal, "Failed to get file info: %v", err)
	}
	content := make([]byte, fileInfo.Size())
	_, err = file.Read(content)
	if err != nil {
		logger.WithField("error", err).Error("Failed to read file")
		return nil, status.Errorf(codes.Internal, "Failed to read file: %v", err)
	}

	return &TS.DownloadTestModuleResponse{
		Content: content,
	}, nil
}

func (s *TestServer) UploadTestModule(ctx context.Context, req *TS.UploadTestModuleRequest) (*TS.UploadTestModuleResponse, error) {
	logger.WithField("name", req.Name).Info("Uploading TestModule")

	testModule := &models.TestModule{}
	if err := s.db.First(testModule, req.Name).Error; err != nil {
		logger.WithField("error", err).Error("Failed to get test module")
		return nil, status.Errorf(codes.Internal, "Failed to get test module: %v", err)
	}

	if testModule.Version == req.Version {
		logger.WithField("error", "version cannot be the same").Error("version cannot be the same")
		return nil, status.Errorf(codes.Internal, "version cannot be the same")
	}

	plugin := testModule.Plugin
	// 检索项目目录下的 modules 目录，是否有 plugin 文件
	modulePath := fmt.Sprintf("modules/%s", plugin)
	if _, err := os.Stat(modulePath); !os.IsNotExist(err) {
		// 如果有，则删除
		if err := os.Remove(modulePath); err != nil {
			logger.WithField("error", err).Error("Failed to delete plugin file")
			return nil, status.Errorf(codes.Internal, "Failed to delete plugin file: %v", err)
		}
	}

	// 将内容写入文件
	file, err := os.Create(modulePath)
	if err != nil {
		logger.WithField("error", err).Error("Failed to create plugin file")
		return nil, status.Errorf(codes.Internal, "Failed to create plugin file: %v", err)
	}
	defer file.Close()

	if _, err := file.Write(req.Content); err != nil {
		logger.WithField("error", err).Error("Failed to write file")
		return nil, status.Errorf(codes.Internal, "Failed to write file: %v", err)
	}

	return &TS.UploadTestModuleResponse{
		TestModule: s.modelToProto(testModule),
	}, nil
}

func (s *TestServer) ApplyTestModule(ctx context.Context, req *TS.ApplyTestModuleRequest) (*TS.ApplyTestModuleResponse, error) {
	logger.WithField("id", req.Id).Info("Applying TestModule")

	testModule := &models.TestModule{}
	if err := s.db.First(testModule, req.Id).Error; err != nil {
		logger.WithField("error", err).Error("Failed to get test module")
		return nil, status.Errorf(codes.Internal, "Failed to get test module: %v", err)
	}

	// 创建测试模块申请
	testModuleApply := &models.TestModuleApply{
		RequestBy: req.RequestBy,
		Modules:   req.Modules,
	}
	if err := s.db.Create(testModuleApply).Error; err != nil {
		logger.WithField("error", err).Error("Failed to create test module apply")
		return nil, status.Errorf(codes.Internal, "Failed to create test module apply: %v", err)
	}

	return &TS.ApplyTestModuleResponse{
		TestModuleApply: s.modelToProtoApply(testModuleApply),
	}, nil
}

func (s *TestServer) ListTestModuleApplies(ctx context.Context, req *TS.ListTestModuleAppliesRequest) (*TS.ListTestModuleAppliesResponse, error) {
	logger.WithFields(map[string]interface{}{
		"page":      req.Page,
		"page_size": req.PageSize,
		"search":    req.Search,
	}).Info("Listing TestModuleApplies")

	var testModuleApplies []*models.TestModuleApply
	if err := s.db.Limit(int(req.PageSize)).Offset(int((req.Page - 1) * req.PageSize)).Find(&testModuleApplies).Error; err != nil {
		logger.WithField("error", err).Error("Failed to list test module applies")
		return nil, status.Errorf(codes.Internal, "Failed to list test module applies: %v", err)
	}

	// 查询总数
	var total int64
	if err := s.db.Model(&models.TestModuleApply{}).Count(&total).Error; err != nil {
		logger.WithField("error", err).Error("Failed to count test module applies")
		return nil, status.Errorf(codes.Internal, "Failed to count test module applies: %v", err)
	}

	return &TS.ListTestModuleAppliesResponse{
		TestModuleApplies: s.modelsToProtosApplies(testModuleApplies),
		Total:             total,
		Page:              req.Page,
		PageSize:          req.PageSize,
	}, nil
}

func (s *TestServer) ProcessTestModuleApply(ctx context.Context, req *TS.ProcessTestModuleApplyRequest) (*TS.ProcessTestModuleApplyResponse, error) {
	logger.WithField("id", req.Id).Info("Processing TestModuleApply")

	testModuleApply := &models.TestModuleApply{}
	if err := s.db.First(testModuleApply, req.Id).Error; err != nil {
		logger.WithField("error", err).Error("Failed to get test module apply")
		return nil, status.Errorf(codes.Internal, "Failed to get test module apply: %v", err)
	}

	testModuleApply.Approved = req.Approved
	if err := s.db.Save(testModuleApply).Error; err != nil {
		logger.WithField("error", err).Error("Failed to update test module apply")
		return nil, status.Errorf(codes.Internal, "Failed to update test module apply: %v", err)
	}

	return &TS.ProcessTestModuleApplyResponse{
		Success: true,
	}, nil
}

func (s *TestServer) CreateTestRecord(ctx context.Context, req *TS.CreateTestRecordRequest) (*TS.CreateTestRecordResponse, error) {
	logger.WithFields(map[string]interface{}{
		"record_number": req.RecordNumber,
		"description":   req.Description,
		"module":        req.Module,
		"created_by":    req.CreatedBy,
	}).Info("Creating TestRecord")

	testRecord := &models.TestRecord{
		RecordNumber: req.RecordNumber,
		Description:  req.Description,
		Module:       req.Module,
		CreatedBy:    req.CreatedBy,
	}

	if err := s.db.Create(testRecord).Error; err != nil {
		logger.WithField("error", err).Error("Failed to create test record")
		return nil, status.Errorf(codes.Internal, "Failed to create test record: %v", err)
	}

	return &TS.CreateTestRecordResponse{
		TestRecord: s.modelToProtoRecord(testRecord),
	}, nil
}

func (s *TestServer) GetTestRecord(ctx context.Context, req *TS.GetTestRecordRequest) (*TS.GetTestRecordResponse, error) {
	logger.WithField("record_number", req.RecordNumber).Info("Getting TestRecord")

	testRecord := &models.TestRecord{}
	if err := s.db.First(testRecord, req.RecordNumber).Error; err != nil {
		logger.WithField("error", err).Error("Failed to get test record")
		return nil, status.Errorf(codes.Internal, "Failed to get test record: %v", err)
	}

	return &TS.GetTestRecordResponse{
		TestRecord: s.modelToProtoRecord(testRecord),
	}, nil
}

func (s *TestServer) DeleteTestRecord(ctx context.Context, req *TS.DeleteTestRecordRequest) (*TS.DeleteTestRecordResponse, error) {
	logger.WithField("record_number", req.RecordNumber).Info("Deleting TestRecord")

	testRecord := &models.TestRecord{}
	if err := s.db.First(testRecord, req.RecordNumber).Error; err != nil {
		logger.WithField("error", err).Error("Failed to get test record")
		return nil, status.Errorf(codes.Internal, "Failed to get test record: %v", err)
	}

	if err := s.db.Delete(testRecord).Error; err != nil {
		logger.WithField("error", err).Error("Failed to delete test record")
		return nil, status.Errorf(codes.Internal, "Failed to delete test record: %v", err)
	}

	return &TS.DeleteTestRecordResponse{
		Success: true,
	}, nil
}

func (s *TestServer) ListTestRecords(ctx context.Context, req *TS.ListTestRecordsRequest) (*TS.ListTestRecordsResponse, error) {
	logger.WithFields(map[string]interface{}{
		"page":      req.Page,
		"page_size": req.PageSize,
		"search":    req.Search,
	}).Info("Listing TestRecords")

	var testRecords []*models.TestRecord
	if err := s.db.Limit(int(req.PageSize)).Offset(int((req.Page - 1) * req.PageSize)).Find(&testRecords).Error; err != nil {
		logger.WithField("error", err).Error("Failed to list test records")
		return nil, status.Errorf(codes.Internal, "Failed to list test records: %v", err)
	}

	// 查询总数
	var total int64
	if err := s.db.Model(&models.TestRecord{}).Count(&total).Error; err != nil {
		logger.WithField("error", err).Error("Failed to count test records")
		return nil, status.Errorf(codes.Internal, "Failed to count test records: %v", err)
	}

	return &TS.ListTestRecordsResponse{
		TestRecords: s.modelsToProtosRecords(testRecords),
		Total:       total,
		Page:        req.Page,
		PageSize:    req.PageSize,
	}, nil
}

func (s *TestServer) CreateTestExecution(ctx context.Context, req *TS.CreateTestExecutionRequest) (*TS.CreateTestExecutionResponse, error) {
	logger.WithFields(map[string]interface{}{
		"test_id":       req.TestId,
		"status":        req.Status,
		"result":        req.Result,
		"error_message": req.ErrorMessage,
		"duration_ms":   req.DurationMs,
		"executed_by":   req.ExecutedBy,
		"environment":   req.Environment,
	}).Info("Creating TestExecution")

	testExecution := &models.TestExecution{
		TestID:       req.TestId,
		Status:       models.TestStatus(req.Status),
		Result:       req.Result,
		ErrorMessage: req.ErrorMessage,
		DurationMs:   req.DurationMs,
		ExecutedBy:   req.ExecutedBy,
		Environment:  req.Environment,
	}

	if err := s.db.Create(testExecution).Error; err != nil {
		logger.WithField("error", err).Error("Failed to create test execution")
		return nil, status.Errorf(codes.Internal, "Failed to create test execution: %v", err)
	}

	return &TS.CreateTestExecutionResponse{
		TestExecution: s.modelToProtoExecution(testExecution),
	}, nil
}

func (s *TestServer) GetTestExecution(ctx context.Context, req *TS.GetTestExecutionRequest) (*TS.GetTestExecutionResponse, error) {
	logger.WithField("id", req.Id).Info("Getting TestExecution")

	testExecution := &models.TestExecution{}
	if err := s.db.First(testExecution, req.Id).Error; err != nil {
		logger.WithField("error", err).Error("Failed to get test execution")
		return nil, status.Errorf(codes.Internal, "Failed to get test execution: %v", err)
	}

	return &TS.GetTestExecutionResponse{
		TestExecution: s.modelToProtoExecution(testExecution),
	}, nil
}

func (s *TestServer) ListTestExecutions(ctx context.Context, req *TS.ListTestExecutionsRequest) (*TS.ListTestExecutionsResponse, error) {
	logger.WithFields(map[string]interface{}{
		"page":      req.Page,
		"page_size": req.PageSize,
		"search":    req.Search,
	}).Info("Listing TestExecutions")

	var testExecutions []*models.TestExecution
	if err := s.db.Limit(int(req.PageSize)).Offset(int((req.Page - 1) * req.PageSize)).Find(&testExecutions).Error; err != nil {
		logger.WithField("error", err).Error("Failed to list test executions")
		return nil, status.Errorf(codes.Internal, "Failed to list test executions: %v", err)
	}

	// 查询总数
	var total int64
	if err := s.db.Model(&models.TestExecution{}).Count(&total).Error; err != nil {
		logger.WithField("error", err).Error("Failed to count test executions")
		return nil, status.Errorf(codes.Internal, "Failed to count test executions: %v", err)
	}

	return &TS.ListTestExecutionsResponse{
		TestExecutions: s.modelsToProtosExecutions(testExecutions),
		Total:          total,
		Page:           req.Page,
		PageSize:       req.PageSize,
	}, nil
}

func (s *TestServer) modelToProto(testModule *models.TestModule) *TS.TestModule {
	return &TS.TestModule{
		Id:          testModule.ID,
		Name:        testModule.Name,
		Plugin:      testModule.Plugin,
		Version:     testModule.Version,
		Description: testModule.Description,
		Itmes:       s.mapToItemsPair(testModule.Items),
		CreatedAt:   timestamppb.New(testModule.CreatedAt),
		UpdatedAt:   timestamppb.New(testModule.UpdatedAt),
	}
}

func (s *TestServer) mapToItemsPair(items map[string]map[string]string) map[string]*TS.KeyValuePair {
	pairs := make(map[string]*TS.KeyValuePair)
	for key, value := range items {
		pairs[key] = &TS.KeyValuePair{Values: value}
	}
	return pairs
}

func (s *TestServer) modelsToProtos(testModules []*models.TestModule) []*TS.TestModule {
	var protos []*TS.TestModule
	for _, testModule := range testModules {
		protos = append(protos, s.modelToProto(testModule))
	}
	return protos
}

func (s *TestServer) modelToProtoApply(testModuleApply *models.TestModuleApply) *TS.TestModuleApply {
	return &TS.TestModuleApply{
		Id:        testModuleApply.ID,
		RequestBy: testModuleApply.RequestBy,
		Modules:   testModuleApply.Modules,
		Approved:  int32(testModuleApply.Approved),
		CreatedAt: timestamppb.New(testModuleApply.CreatedAt),
	}
}

func (s *TestServer) modelsToProtosApplies(testModuleApplies []*models.TestModuleApply) []*TS.TestModuleApply {
	var protos []*TS.TestModuleApply
	for _, testModuleApply := range testModuleApplies {
		protos = append(protos, s.modelToProtoApply(testModuleApply))
	}
	return protos
}

func (s *TestServer) modelToProtoRecord(testRecord *models.TestRecord) *TS.TestRecord {
	return &TS.TestRecord{
		Id:           testRecord.ID,
		RecordNumber: testRecord.RecordNumber,
		Description:  testRecord.Description,
		Module:       testRecord.Module,
		CreatedBy:    testRecord.CreatedBy,
		CreatedAt:    timestamppb.New(testRecord.CreatedAt),
		UpdatedAt:    timestamppb.New(testRecord.UpdatedAt),
	}
}

func (s *TestServer) modelsToProtosRecords(testRecords []*models.TestRecord) []*TS.TestRecord {
	var protos []*TS.TestRecord
	for _, testRecord := range testRecords {
		protos = append(protos, s.modelToProtoRecord(testRecord))
	}
	return protos
}

func (s *TestServer) modelToProtoExecution(testExecution *models.TestExecution) *TS.TestExecution {
	return &TS.TestExecution{
		Id:           testExecution.ID,
		TestId:       testExecution.TestID,
		Status:       s.statusStringToInt32(testExecution.Status),
		Result:       testExecution.Result,
		ErrorMessage: testExecution.ErrorMessage,
		DurationMs:   testExecution.DurationMs,
		ExecutedBy:   testExecution.ExecutedBy,
		Environment:  testExecution.Environment,
		StartedAt:    timestamppb.New(testExecution.StartedAt),
		FinishedAt:   timestamppb.New(testExecution.FinishedAt),
		CreatedAt:    timestamppb.New(testExecution.CreatedAt),
		UpdatedAt:    timestamppb.New(testExecution.UpdatedAt),
	}
}

func (s *TestServer) modelsToProtosExecutions(testExecutions []*models.TestExecution) []*TS.TestExecution {
	var protos []*TS.TestExecution
	for _, testExecution := range testExecutions {
		protos = append(protos, s.modelToProtoExecution(testExecution))
	}
	return protos
}

func (s *TestServer) statusStringToInt32(status models.TestStatus) TS.TestStatus {
	switch status {
	case "pending":
		return TS.TestStatus_PENDING
	case "running":
		return TS.TestStatus_RUNNING
	case "passed":
		return TS.TestStatus_PASSED
	case "failed":
		return TS.TestStatus_FAILED
	case "skipped":
		return TS.TestStatus_SKIPPED
	default:
		return TS.TestStatus_PENDING
	}
}

// func (s *TestServer) statusInt32ToString(status int32) string {
// 	switch status {
// 	case int32(TS.TestStatus_PENDING):
// 		return "pending"
// 	case int32(TS.TestStatus_RUNNING):
// 		return "running"
// 	case int32(TS.TestStatus_PASSED):
// 		return "passed"
// 	case int32(TS.TestStatus_FAILED):
// 		return "failed"
// 	case int32(TS.TestStatus_SKIPPED):
// 		return "skipped"
// 	default:
// 		return "pending"
// 	}
// }

func (s *TestServer) itemsPairToMap(items map[string]*TS.KeyValuePair) map[string]map[string]string {
	pairs := make(map[string]map[string]string)
	for key, value := range items {
		pairs[key] = value.Values
	}
	return pairs
}
