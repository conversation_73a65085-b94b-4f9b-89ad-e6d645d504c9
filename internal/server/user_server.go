package server

import (
	"context"
	"fmt"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"
	US "thinta.test.platform/api/proto/user"
	"thinta.test.platform/internal/shared/models"
	"thinta.test.platform/pkg/logger"
	"thinta.test.platform/pkg/model"
)

type UserServer struct {
	US.UnimplementedUserServiceServer
	db *gorm.DB
}

func NewUserService(db *gorm.DB) *UserServer {
	return &UserServer{db: db}
}

// 获取服务信息
func (s *UserServer) GetServiceInfo(ctx context.Context, req *US.GetServiceInfoRequest) (*US.GetServiceInfoResponse, error) {
	return &US.GetServiceInfoResponse{
		Name:        "user-service",
		Version:     "1.0.0",
		Time:        time.Now().Format("2006-01-02 15:04:05.000"),
		Description: "振中测试平台-用户管理服务系统",
	}, nil
}

// 注册
func (s *UserServer) Register(ctx context.Context, req *US.RegisterRequest) (*US.RegisterResponse, error) {
	logger.WithFields(map[string]interface{}{
		"username": req.Username,
		"email":    req.Email,
	}).Info("Creating user")

	err := model.Register(req.Username, req.Email, req.FullName)
	if err != nil {
		logger.WithField("error", err).Error("Failed to create user")
		return nil, status.Errorf(codes.Internal, "Failed to create user: %v", err)
	}
	return &US.RegisterResponse{}, nil
}

// 登录
func (s *UserServer) Login(ctx context.Context, req *US.LoginRequest) (*US.LoginResponse, error) {
	logger.WithField("username", req.Username).Info("Logging in")
	currentUser, err := model.Login(req.Username, req.Password)
	if err != nil {
		logger.WithField("error", err).Error("Failed to generate token")
		return nil, status.Errorf(codes.Internal, "Failed to generate token: %v", err)
	}

	return &US.LoginResponse{
		User:  s.modelToProto(currentUser.User),
		Token: currentUser.Token,
	}, nil
}

// 登出
func (s *UserServer) Logout(ctx context.Context, req *US.LogoutRequest) (*US.LogoutResponse, error) {
	logger.WithField("token", req.Token).Info("Logging out")

	token, err := getToken(ctx)
	if err != nil {
		return nil, err
	}

	if err := model.Logout(token); err != nil {
		logger.WithField("error", err).Error("Failed to logout")
		return nil, status.Errorf(codes.Internal, "Failed to logout: %v", err)
	}
	return &US.LogoutResponse{}, nil
}

// 获取用户列表
func (s *UserServer) GetUsers(ctx context.Context, req *US.GetUsersRequest) (*US.GetUsersResponse, error) {
	logger.WithField("page", req.Page).Info("Getting users")

	token, err := getToken(ctx)
	if err != nil {
		return nil, err
	}

	// 查询用户列表
	var users []models.User
	if req.Search != "" {
		// 构造搜索条件：按用户名搜索
		condition := fmt.Sprintf("username = '%s'", req.Search)
		users, err = model.GetUserByCondition(token, int(req.Page), int(req.PageSize), condition)
	} else {
		// 如果没有搜索条件，获取所有用户
		users, err = model.GetUsers(token, int(req.Page), int(req.PageSize))
	}
	if err != nil {
		logger.WithField("error", err).Error("Failed to get users")
		return nil, status.Errorf(codes.Internal, "Failed to get users: %v", err)
	}

	return &US.GetUsersResponse{
		Users: s.modelsToProtos(users),
	}, nil
}

// 更新用户
func (s *UserServer) UpdateUser(ctx context.Context, req *US.UpdateUserRequest) (*US.UpdateUserResponse, error) {
	logger.WithField("username", req.Username).Info("Updating user")

	token, err := getToken(ctx)
	if err != nil {
		return nil, err
	}

	user, err := model.UpdateUserByParam(token, req.Id, req.Username, req.Email, req.FullName, -1, -1, nil)

	if err != nil {
		logger.WithField("error", err).Error("Failed to update user")
		return nil, status.Errorf(codes.Internal, "Failed to update user: %v", err)
	}

	return &US.UpdateUserResponse{
		User: s.modelToProto(user),
	}, nil
}

// 修改密码
func (s *UserServer) ChangeUserPassword(ctx context.Context, req *US.ChangeUserPasswordRequest) (*US.ChangeUserPasswordResponse, error) {
	logger.WithField("username", req.Username).Info("Changing user password")

	token, err := getToken(ctx)
	if err != nil {
		return nil, err
	}

	err = model.ChangeUserPassword(token, req.Username, req.OldPassword, req.NewPassword)
	if err != nil {
		logger.WithField("error", err).Error("Failed to change password")
		return nil, status.Errorf(codes.Internal, "Failed to change password: %v", err)
	}

	return &US.ChangeUserPasswordResponse{}, nil
}

// 修改用户角色
func (s *UserServer) ChangeUserRole(ctx context.Context, req *US.ChangeUserRoleRequest) (*US.ChangeUserRoleResponse, error) {
	logger.WithField("username", req.Username).Info("Changing user role")

	token, err := getToken(ctx)
	if err != nil {
		return nil, err
	}

	user, err := model.UpdateUserByParam(token, req.Id, "", "", "", req.Role, -1, nil)
	if err != nil {
		logger.WithField("error", err).Error("Failed to change user role")
		return nil, status.Errorf(codes.Internal, "Failed to change user role: %v", err)
	}
	return &US.ChangeUserRoleResponse{
		User: s.modelToProto(user),
	}, nil
}

// 修改用户状态
func (s *UserServer) ChangeUserStatus(ctx context.Context, req *US.ChangeUserStatusRequest) (*US.ChangeUserStatusResponse, error) {
	logger.WithField("username", req.Username).Info("Changing user status")

	token, err := getToken(ctx)
	if err != nil {
		return nil, err
	}

	user, err := model.UpdateUserByParam(token, req.Id, "", "", "", -1, req.Status, nil)
	if err != nil {
		logger.WithField("error", err).Error("Failed to change user status")
		return nil, status.Errorf(codes.Internal, "Failed to change user status: %v", err)
	}
	return &US.ChangeUserStatusResponse{
		User: s.modelToProto(user),
	}, nil
}

// 修改用户测试模块
func (s *UserServer) ChangeUserTestModules(ctx context.Context, req *US.ChangeUserTestModulesRequest) (*US.ChangeUserTestModulesResponse, error) {
	logger.WithField("username", req.Username).Info("Changing user test modules")

	token, err := getToken(ctx)
	if err != nil {
		return nil, err
	}

	user, err := model.UpdateUserByParam(token, req.Id, "", "", "", -1, -1, req.TestModules)
	if err != nil {
		logger.WithField("error", err).Error("Failed to change user test modules")
		return nil, status.Errorf(codes.Internal, "Failed to change user test modules: %v", err)
	}
	return &US.ChangeUserTestModulesResponse{
		User: s.modelToProto(user),
	}, nil
}

// 删除用户
func (s *UserServer) DeleteUser(ctx context.Context, req *US.DeleteUserRequest) (*US.DeleteUserResponse, error) {
	logger.WithField("username", req.Username).Info("Deleting user")

	token, err := getToken(ctx)
	if err != nil {
		return nil, err
	}

	if err := model.DeleteUser(token, req.Username); err != nil {
		logger.WithField("error", err).Error("Failed to delete user")
		return nil, status.Errorf(codes.Internal, "Failed to delete user: %v", err)
	}
	return &US.DeleteUserResponse{}, nil
}

func (s *UserServer) AuthenticateUser(ctx context.Context, req *US.AuthenticateUserRequest) (*US.AuthenticateUserResponse, error) {
	// logger.WithField("username", req.Username).Info("Authenticating user")

	token, err := getToken(ctx)
	if err != nil {
		return nil, err
	}

	err = model.AuthenticateUser(token, req.Password)
	if err != nil {
		logger.WithField("error", err).Error("Failed to authenticate user")
		return nil, status.Errorf(codes.Internal, "Failed to authenticate user: %v", err)
	}

	return &US.AuthenticateUserResponse{}, nil
}

func (s *UserServer) ResetUserPassword(ctx context.Context, req *US.ResetUserPasswordRequest) (*US.ResetUserPasswordResponse, error) {
	logger.WithField("username", req.Username).Info("Resetting user password")

	token, err := getToken(ctx)
	if err != nil {
		return nil, err
	}

	if err := model.ResetUserPassword(token, req.Username); err != nil {
		logger.WithField("error", err).Error("Failed to reset user password")
		return nil, status.Errorf(codes.Internal, "Failed to reset user password: %v", err)
	}

	return &US.ResetUserPasswordResponse{}, nil
}

func getToken(ctx context.Context) (string, error) {
	md, _ := metadata.FromIncomingContext(ctx)
	tokens := md.Get("authorization")
	if len(tokens) == 0 {
		return "", status.Error(codes.Unauthenticated, "Token required")
	}
	return tokens[0], nil
}

// modelToProto 将模型转换为 protobuf
func (s *UserServer) modelToProto(user *models.User) *US.User {
	return &US.User{
		Id:        user.ID,
		Username:  user.Username,
		Email:     user.Email,
		FullName:  user.FullName,
		Status:    user.Status,
		CreatedAt: timestamppb.New(user.CreatedAt),
		UpdatedAt: timestamppb.New(user.UpdatedAt),
	}
}

// modelsToProtos 将模型列表转换为 protobuf 列表
func (s *UserServer) modelsToProtos(users []models.User) []*US.User {
	var protos []*US.User
	for _, user := range users {
		protos = append(protos, s.modelToProto(&user))
	}
	return protos
}
