package server

import (
	"context"
	"testing"
	"time"

	"google.golang.org/grpc/metadata"
	US "thinta.test.platform/api/proto/user"
	"thinta.test.platform/pkg/config"
	"thinta.test.platform/pkg/database"
)

func TestGetServiceInfo(t *testing.T) {
	t.Run("GetServiceInfo", func(t *testing.T) {
		// 创建用户请求
		req := &US.GetServiceInfoRequest{}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewUserService(database.PlatformDB)
		// 调用 GetServiceInfo 方法
		resp, err := s.GetServiceInfo(context.Background(), req)
		if err != nil {
			t.Errorf("GetServiceInfo failed: %v", err)
			return
		}

		t.Logf("GetServiceInfo success: %v", resp)
	})
}

func TestCreateUser(t *testing.T) {
	// 测试创建账户信息
	t.Run("CreateUser", func(t *testing.T) {
		// 创建用户请求
		req := &US.RegisterRequest{
			Username: "T-202506111340",
			Email:    "<EMAIL>",
			FullName: "TUser202506111340",
		}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewUserService(database.PlatformDB)
		// 调用 CreateUser 方法
		resp, err := s.Register(context.Background(), req)
		if err != nil {
			t.Errorf("Register failed: %v", err)
			return
		}

		// 检查响应
		t.Logf("Register success: %v", resp)
	})
}

// 集成测试
// 1. 创建用户
// 2. 新创建用户登录（测试预期为失败）
// 3. 管理员登录
// 4. 修改新建用户状态及角色
// 5. 修改新建用户密码为 12345678
// 6. 新创建用户登录（测试预期为成功）
// 7. 新创建用户修改密码为 11111111
// 8. 新创建用户修改自身状态及角色（预期失败）
// 9. 新创建用户登出
// 10. 新建用户修改自身账户信息（预期失败，确认已登出）
// 11. 管理员退出登录
// 12. 管理员获取全部用户信息（预期失败，确认已登出）
func TestIntegration(t *testing.T) {
	t.Run("Integration", func(t *testing.T) {
		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewUserService(database.PlatformDB)
		// 获取当前时间，格式化为 yyyyMMddHHmm
		now := time.Now()
		username := now.Format("200601021504")
		email := "TEmail" + username + "@test.com"
		fullName := "TUser" + username

		// 1. 创建用户
		req1 := &US.RegisterRequest{
			Username: username,
			Email:    email,
			FullName: fullName,
		}
		resp1, err := s.Register(context.Background(), req1)
		if err != nil {
			t.Errorf("Register failed: %v", err)
			return
		}
		t.Logf("Register success: %v", resp1)

		// 2. 新创建用户登录
		req2 := &US.LoginRequest{
			Username: username,
			Password: "11111111",
		}
		_, err = s.Login(context.Background(), req2)
		if err == nil {
			t.Errorf("User login success, but expected failed")
			return
		}
		t.Logf("User login failed as expected: %v", err)

		// 3. 管理员登录
		req3 := &US.LoginRequest{
			Username: "admin",
			Password: "admin",
		}
		resp3, err := s.Login(context.Background(), req3)
		if err != nil {
			t.Errorf("Admain login failed: %v", err)
			return
		}
		t.Logf("Admin login success: %v", resp3)
		// 创建管理员上下文
		adminToken := resp3.Token
		adminMD := metadata.Pairs("authorization", adminToken)
		adminCtx := metadata.NewIncomingContext(context.Background(), adminMD)

		// 4. 修改新建用户状态及角色
		req4_1 := &US.GetUsersRequest{
			Page:     1,
			PageSize: 10,
			Search:   username,
		}
		resp4_1, err := s.GetUsers(adminCtx, req4_1)
		if err != nil {
			t.Errorf("GetUsers failed: %v", err)
			return
		}
		t.Logf("GetUsers success: %v", resp4_1)
		user := resp4_1.Users[0]
		req4_2 := &US.ChangeUserStatusRequest{
			Id:       user.Id,
			Username: username,
			Status:   1,
		}
		resp4_2, err := s.ChangeUserStatus(adminCtx, req4_2)
		if err != nil {
			t.Errorf("ChangeUserStatus failed: %v", err)
			return
		}
		t.Logf("ChangeUserStatus success: %v", resp4_2)
		req4_3 := &US.ChangeUserRoleRequest{
			Id:       user.Id,
			Username: username,
			Role:     3,
		}
		resp4_3, err := s.ChangeUserRole(adminCtx, req4_3)
		if err != nil {
			t.Errorf("ChangeUserRole failed: %v", err)
			return
		}
		t.Logf("ChangeUserRole success: %v", resp4_3)

		// 5. 修改新建用户密码为 12345678
		req5 := &US.ChangeUserPasswordRequest{
			Username:    username,
			OldPassword: "11111111",
			NewPassword: "12345678",
		}
		resp5, err := s.ChangeUserPassword(adminCtx, req5)
		if err != nil {
			t.Errorf("ChangeUserPassword failed: %v", err)
			return
		}
		t.Logf("ChangeUserPassword success: %v", resp5)

		// 6. 新创建用户登录（测试预期为成功）
		req6 := &US.LoginRequest{
			Username: username,
			Password: "12345678",
		}
		resp6, err := s.Login(context.Background(), req6)
		if err != nil {
			t.Errorf("User login failed: %v", err)
			return
		}
		t.Logf("User login success: %v", resp6)
		// 创建用户上下文
		userToken := resp6.Token
		userMD := metadata.Pairs("authorization", userToken)
		userCtx := metadata.NewIncomingContext(context.Background(), userMD)

		// 7. 新创建用户修改密码为 11111111
		req7 := &US.ChangeUserPasswordRequest{
			Username:    username,
			OldPassword: "12345678",
			NewPassword: "11111111",
		}
		resp7, err := s.ChangeUserPassword(userCtx, req7)
		if err != nil {
			t.Errorf("ChangeUserPassword failed: %v", err)
			return
		}
		t.Logf("ChangeUserPassword success: %v", resp7)

		// 8. 新创建用户修改自身状态及角色（预期失败）
		req8_1 := &US.ChangeUserStatusRequest{
			Id:       user.Id,
			Username: username,
			Status:   2,
		}
		_, err = s.ChangeUserStatus(userCtx, req8_1)
		if err == nil {
			t.Errorf("User change status success, but expected failed")
			return
		}
		t.Logf("User change status failed as expected: %v", err)

		req8_2 := &US.ChangeUserRoleRequest{
			Id:       user.Id,
			Username: username,
			Role:     2,
		}
		_, err = s.ChangeUserRole(userCtx, req8_2)
		if err == nil {
			t.Errorf("User change role success, but expected failed")
			return
		}
		t.Logf("User change role failed as expected: %v", err)

		// 9. 新创建用户登出
		req9 := &US.LogoutRequest{
			Token: userToken,
		}
		resp9, err := s.Logout(userCtx, req9)
		if err != nil {
			t.Errorf("User logout failed: %v", err)
			return
		}
		t.Logf("User logout success: %v", resp9)

		// 10. 新建用户修改自身账户信息（预期失败，确认已登出）
		req10 := &US.UpdateUserRequest{
			Id:       user.Id,
			Username: username,
			Email:    "<EMAIL>",
			FullName: "New User",
		}
		_, err = s.UpdateUser(userCtx, req10)
		if err == nil {
			t.Errorf("User update success, but expected failed")
			return
		}
		t.Logf("User update failed as expected: %v", err)

		// 11. 管理员退出登录
		req11 := &US.LogoutRequest{
			Token: adminToken,
		}
		resp11, err := s.Logout(adminCtx, req11)
		if err != nil {
			t.Errorf("Admin logout failed: %v", err)
			return
		}
		t.Logf("Admin logout success: %v", resp11)

		// 12. 管理员获取全部用户信息（预期失败，确认已登出）
		_, err = s.GetUsers(adminCtx, req4_1)
		if err == nil {
			t.Errorf("Admin get users success, but expected failed")
			return
		}
		t.Logf("Admin get users failed as expected: %v", err)

	})
}

func TestLogin(t *testing.T) {
	t.Run("Login", func(t *testing.T) {
		// 创建用户请求
		// req := &US.LoginRequest{
		// 	Username: "T-202506111340",
		// 	Password: "11111111",
		// }
		req := &US.LoginRequest{
			Username: "admin",
			Password: "admin",
		}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewUserService(database.PlatformDB)
		// 调用 Login 方法
		resp, err := s.Login(context.Background(), req)
		if err != nil {
			t.Errorf("Login failed: %v", err)
			return
		}

		t.Logf("Login success: %v", resp)
	})
}

func TestLogout(t *testing.T) {
	t.Run("Logout", func(t *testing.T) {
		// 创建用户请求
		req := &US.LogoutRequest{
			Token: "test",
		}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewUserService(database.PlatformDB)
		// 调用 Logout 方法
		resp, err := s.Logout(context.Background(), req)
		if err != nil {
			t.Errorf("Logout failed: %v", err)
			return
		}

		t.Logf("Logout sucess: %v", resp)
	})
}

func TestListUsers(t *testing.T) {
	t.Run("ListUsers", func(t *testing.T) {
		// 创建用户请求
		req := &US.GetUsersRequest{
			Page:     1,
			PageSize: 10,
		}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewUserService(database.PlatformDB)
		// 调用 ListUsers 方法
		resp, err := s.GetUsers(context.Background(), req)
		if err != nil {
			t.Errorf("ListUsers failed: %v", err)
		}

		// 检查响应
		if len(resp.Users) == 0 {
			t.Error("Expected users in response")
		} else {
			t.Logf("ListUsers success: %v", resp.Users)
		}
	})
}

func TestUpdateUser(t *testing.T) {
	t.Run("UpdateUser", func(t *testing.T) {
		// 创建用户请求
		req := &US.UpdateUserRequest{
			Id:       1,
			Username: "test",
			Email:    "<EMAIL>",
			FullName: "Test User",
		}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewUserService(database.PlatformDB)
		// 调用 UpdateUser 方法
		resp, err := s.UpdateUser(context.Background(), req)
		if err != nil {
			t.Errorf("UpdateUser failed: %v", err)
			return
		}

		// 检查响应
		t.Logf("UpdateUser Success: %v", resp)
	})
}

func TestChangeUserPassword(t *testing.T) {
	t.Run("ChangeUserPassword", func(t *testing.T) {
		// 创建用户请求
		req := &US.ChangeUserPasswordRequest{
			Username:    "test",
			OldPassword: "11111111",
			NewPassword: "12345678",
		}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewUserService(database.PlatformDB)
		// 调用 ChangeUserPassword 方法
		resp, err := s.ChangeUserPassword(context.Background(), req)
		// t.Logf("ChangeUserPassword err: %s", err)
		if err != nil {
			t.Errorf("ChangeUserPassword failed: %v", err)
			return
		}

		t.Logf("ChangeUserPassword success: %v", resp)
	})
}

func TestChangeRole(t *testing.T) {
	t.Run("ChangeUserRole", func(t *testing.T) {
		// 创建用户请求
		req := &US.ChangeUserRoleRequest{
			Id:       1,
			Username: "test",
			Role:     2,
		}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewUserService(database.PlatformDB)
		// 调用 ChangeUserRole 方法
		resp, err := s.ChangeUserRole(context.Background(), req)
		if err != nil {
			t.Errorf("ChangeUserRole failed: %v", err)
			return
		}

		t.Logf("ChangeUserRole success: %v", resp)
	})
}

func TestChangeStatus(t *testing.T) {
	t.Run("ChangeUserStatus", func(t *testing.T) {
		// 创建用户请求
		req := &US.ChangeUserStatusRequest{
			Id:       1,
			Username: "test",
			Status:   2,
		}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewUserService(database.PlatformDB)
		// 调用 ChangeUserStatus 方法
		resp, err := s.ChangeUserStatus(context.Background(), req)
		if err != nil {
			t.Errorf("ChangeUserStatus failed: %v", err)
			return
		}

		t.Logf("ChangeUserStatus success: %v", resp)
	})
}

func TestDeleteUser(t *testing.T) {
	t.Run("DeleteUser", func(t *testing.T) {
		// 创建用户请求
		req := &US.DeleteUserRequest{
			Username: "test",
		}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewUserService(database.PlatformDB)
		// 调用 DeleteUser 方法
		resp, err := s.DeleteUser(context.Background(), req)
		if err != nil {
			t.Errorf("DeleteUser failed: %v", err)
			return
		}

		t.Logf("DeleteUser success: %v", resp)
	})
}

func TestAuthenticateUser(t *testing.T) {
	t.Run("AuthenticateUser", func(t *testing.T) {
		// 创建用户请求
		req := &US.AuthenticateUserRequest{
			Password: "123456",
		}

		// 加载配置
		cfg, err := config.Load("../../configs")
		if err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		// 创建数据库连接
		err = database.NewPlatformDB(cfg.Database)
		if err != nil {
			t.Fatalf("Failed to create database connection: %v", err)
		}

		s := NewUserService(database.PlatformDB)
		// 调用 AuthenticateUser 方法
		resp, err := s.AuthenticateUser(context.Background(), req)
		if err != nil {
			t.Errorf("AuthenticateUser failed: %v", err)
			return
		}

		t.Logf("AuthenticateUser success: %v", resp)
	})
}
