// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.12.4
// source: api/proto/user/user.proto

package user

import (
	timestamp "github.com/golang/protobuf/ptypes/timestamp"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 用户信息
type User struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Username      string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Password      string                 `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
	Email         string                 `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	FullName      string                 `protobuf:"bytes,5,opt,name=full_name,json=fullName,proto3" json:"full_name,omitempty"`
	Role          int32                  `protobuf:"varint,6,opt,name=role,proto3" json:"role,omitempty"`
	Status        int32                  `protobuf:"varint,7,opt,name=status,proto3" json:"status,omitempty"`
	TestModules   []string               `protobuf:"bytes,8,rep,name=test_modules,json=testModules,proto3" json:"test_modules,omitempty"`
	CreatedAt     *timestamp.Timestamp   `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamp.Timestamp   `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *User) Reset() {
	*x = User{}
	mi := &file_api_proto_user_user_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_user_user_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_api_proto_user_user_proto_rawDescGZIP(), []int{0}
}

func (x *User) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *User) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *User) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *User) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *User) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *User) GetRole() int32 {
	if x != nil {
		return x.Role
	}
	return 0
}

func (x *User) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *User) GetTestModules() []string {
	if x != nil {
		return x.TestModules
	}
	return nil
}

func (x *User) GetCreatedAt() *timestamp.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *User) GetUpdatedAt() *timestamp.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// 获取服务信息请求
type GetServiceInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceInfoRequest) Reset() {
	*x = GetServiceInfoRequest{}
	mi := &file_api_proto_user_user_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceInfoRequest) ProtoMessage() {}

func (x *GetServiceInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_user_user_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceInfoRequest.ProtoReflect.Descriptor instead.
func (*GetServiceInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_user_user_proto_rawDescGZIP(), []int{1}
}

// 获取服务信息响应
type GetServiceInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Version       string                 `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Time          string                 `protobuf:"bytes,3,opt,name=time,proto3" json:"time,omitempty"`
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceInfoResponse) Reset() {
	*x = GetServiceInfoResponse{}
	mi := &file_api_proto_user_user_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceInfoResponse) ProtoMessage() {}

func (x *GetServiceInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_user_user_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceInfoResponse.ProtoReflect.Descriptor instead.
func (*GetServiceInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_user_user_proto_rawDescGZIP(), []int{2}
}

func (x *GetServiceInfoResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetServiceInfoResponse) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *GetServiceInfoResponse) GetTime() string {
	if x != nil {
		return x.Time
	}
	return ""
}

func (x *GetServiceInfoResponse) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// 注册请求
type RegisterRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Username      string                 `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	Email         string                 `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	FullName      string                 `protobuf:"bytes,3,opt,name=full_name,json=fullName,proto3" json:"full_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterRequest) Reset() {
	*x = RegisterRequest{}
	mi := &file_api_proto_user_user_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterRequest) ProtoMessage() {}

func (x *RegisterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_user_user_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterRequest.ProtoReflect.Descriptor instead.
func (*RegisterRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_user_user_proto_rawDescGZIP(), []int{3}
}

func (x *RegisterRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *RegisterRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *RegisterRequest) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

// 注册响应
type RegisterResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterResponse) Reset() {
	*x = RegisterResponse{}
	mi := &file_api_proto_user_user_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterResponse) ProtoMessage() {}

func (x *RegisterResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_user_user_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterResponse.ProtoReflect.Descriptor instead.
func (*RegisterResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_user_user_proto_rawDescGZIP(), []int{4}
}

// 登录请求
type LoginRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Username      string                 `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	Password      string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginRequest) Reset() {
	*x = LoginRequest{}
	mi := &file_api_proto_user_user_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginRequest) ProtoMessage() {}

func (x *LoginRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_user_user_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginRequest.ProtoReflect.Descriptor instead.
func (*LoginRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_user_user_proto_rawDescGZIP(), []int{5}
}

func (x *LoginRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *LoginRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

// 登录响应
type LoginResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          *User                  `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Token         string                 `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginResponse) Reset() {
	*x = LoginResponse{}
	mi := &file_api_proto_user_user_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginResponse) ProtoMessage() {}

func (x *LoginResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_user_user_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginResponse.ProtoReflect.Descriptor instead.
func (*LoginResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_user_user_proto_rawDescGZIP(), []int{6}
}

func (x *LoginResponse) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *LoginResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// 登出请求
type LogoutRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LogoutRequest) Reset() {
	*x = LogoutRequest{}
	mi := &file_api_proto_user_user_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogoutRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogoutRequest) ProtoMessage() {}

func (x *LogoutRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_user_user_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogoutRequest.ProtoReflect.Descriptor instead.
func (*LogoutRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_user_user_proto_rawDescGZIP(), []int{7}
}

func (x *LogoutRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// 登出响应
type LogoutResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LogoutResponse) Reset() {
	*x = LogoutResponse{}
	mi := &file_api_proto_user_user_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogoutResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogoutResponse) ProtoMessage() {}

func (x *LogoutResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_user_user_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogoutResponse.ProtoReflect.Descriptor instead.
func (*LogoutResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_user_user_proto_rawDescGZIP(), []int{8}
}

// 获取用户列表请求
type GetUsersRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Search        string                 `protobuf:"bytes,3,opt,name=search,proto3" json:"search,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUsersRequest) Reset() {
	*x = GetUsersRequest{}
	mi := &file_api_proto_user_user_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUsersRequest) ProtoMessage() {}

func (x *GetUsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_user_user_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUsersRequest.ProtoReflect.Descriptor instead.
func (*GetUsersRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_user_user_proto_rawDescGZIP(), []int{9}
}

func (x *GetUsersRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetUsersRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetUsersRequest) GetSearch() string {
	if x != nil {
		return x.Search
	}
	return ""
}

// 获取用户列表响应
type GetUsersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Users         []*User                `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUsersResponse) Reset() {
	*x = GetUsersResponse{}
	mi := &file_api_proto_user_user_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUsersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUsersResponse) ProtoMessage() {}

func (x *GetUsersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_user_user_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUsersResponse.ProtoReflect.Descriptor instead.
func (*GetUsersResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_user_user_proto_rawDescGZIP(), []int{10}
}

func (x *GetUsersResponse) GetUsers() []*User {
	if x != nil {
		return x.Users
	}
	return nil
}

// 更新用户请求
type UpdateUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Username      string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Email         string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	FullName      string                 `protobuf:"bytes,4,opt,name=full_name,json=fullName,proto3" json:"full_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserRequest) Reset() {
	*x = UpdateUserRequest{}
	mi := &file_api_proto_user_user_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserRequest) ProtoMessage() {}

func (x *UpdateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_user_user_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_user_user_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateUserRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateUserRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *UpdateUserRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UpdateUserRequest) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

// 更新用户响应
type UpdateUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          *User                  `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserResponse) Reset() {
	*x = UpdateUserResponse{}
	mi := &file_api_proto_user_user_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserResponse) ProtoMessage() {}

func (x *UpdateUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_user_user_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserResponse.ProtoReflect.Descriptor instead.
func (*UpdateUserResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_user_user_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateUserResponse) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

// 修改密码请求
type ChangeUserPasswordRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Username      string                 `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	OldPassword   string                 `protobuf:"bytes,2,opt,name=old_password,json=oldPassword,proto3" json:"old_password,omitempty"`
	NewPassword   string                 `protobuf:"bytes,3,opt,name=new_password,json=newPassword,proto3" json:"new_password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangeUserPasswordRequest) Reset() {
	*x = ChangeUserPasswordRequest{}
	mi := &file_api_proto_user_user_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangeUserPasswordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeUserPasswordRequest) ProtoMessage() {}

func (x *ChangeUserPasswordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_user_user_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeUserPasswordRequest.ProtoReflect.Descriptor instead.
func (*ChangeUserPasswordRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_user_user_proto_rawDescGZIP(), []int{13}
}

func (x *ChangeUserPasswordRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ChangeUserPasswordRequest) GetOldPassword() string {
	if x != nil {
		return x.OldPassword
	}
	return ""
}

func (x *ChangeUserPasswordRequest) GetNewPassword() string {
	if x != nil {
		return x.NewPassword
	}
	return ""
}

// 修改密码响应
type ChangeUserPasswordResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangeUserPasswordResponse) Reset() {
	*x = ChangeUserPasswordResponse{}
	mi := &file_api_proto_user_user_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangeUserPasswordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeUserPasswordResponse) ProtoMessage() {}

func (x *ChangeUserPasswordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_user_user_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeUserPasswordResponse.ProtoReflect.Descriptor instead.
func (*ChangeUserPasswordResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_user_user_proto_rawDescGZIP(), []int{14}
}

// 修改用户角色
type ChangeUserRoleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Username      string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Role          int32                  `protobuf:"varint,3,opt,name=role,proto3" json:"role,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangeUserRoleRequest) Reset() {
	*x = ChangeUserRoleRequest{}
	mi := &file_api_proto_user_user_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangeUserRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeUserRoleRequest) ProtoMessage() {}

func (x *ChangeUserRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_user_user_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeUserRoleRequest.ProtoReflect.Descriptor instead.
func (*ChangeUserRoleRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_user_user_proto_rawDescGZIP(), []int{15}
}

func (x *ChangeUserRoleRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ChangeUserRoleRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ChangeUserRoleRequest) GetRole() int32 {
	if x != nil {
		return x.Role
	}
	return 0
}

// 修改用户角色响应
type ChangeUserRoleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          *User                  `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangeUserRoleResponse) Reset() {
	*x = ChangeUserRoleResponse{}
	mi := &file_api_proto_user_user_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangeUserRoleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeUserRoleResponse) ProtoMessage() {}

func (x *ChangeUserRoleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_user_user_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeUserRoleResponse.ProtoReflect.Descriptor instead.
func (*ChangeUserRoleResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_user_user_proto_rawDescGZIP(), []int{16}
}

func (x *ChangeUserRoleResponse) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

// 修改用户状态
type ChangeUserStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Username      string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Status        int32                  `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangeUserStatusRequest) Reset() {
	*x = ChangeUserStatusRequest{}
	mi := &file_api_proto_user_user_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangeUserStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeUserStatusRequest) ProtoMessage() {}

func (x *ChangeUserStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_user_user_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeUserStatusRequest.ProtoReflect.Descriptor instead.
func (*ChangeUserStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_user_user_proto_rawDescGZIP(), []int{17}
}

func (x *ChangeUserStatusRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ChangeUserStatusRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ChangeUserStatusRequest) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

// 修改用户状态响应
type ChangeUserStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          *User                  `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangeUserStatusResponse) Reset() {
	*x = ChangeUserStatusResponse{}
	mi := &file_api_proto_user_user_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangeUserStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeUserStatusResponse) ProtoMessage() {}

func (x *ChangeUserStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_user_user_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeUserStatusResponse.ProtoReflect.Descriptor instead.
func (*ChangeUserStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_user_user_proto_rawDescGZIP(), []int{18}
}

func (x *ChangeUserStatusResponse) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

// 修改用户测试模块
type ChangeUserTestModulesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Username      string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	TestModules   []string               `protobuf:"bytes,3,rep,name=test_modules,json=testModules,proto3" json:"test_modules,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangeUserTestModulesRequest) Reset() {
	*x = ChangeUserTestModulesRequest{}
	mi := &file_api_proto_user_user_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangeUserTestModulesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeUserTestModulesRequest) ProtoMessage() {}

func (x *ChangeUserTestModulesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_user_user_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeUserTestModulesRequest.ProtoReflect.Descriptor instead.
func (*ChangeUserTestModulesRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_user_user_proto_rawDescGZIP(), []int{19}
}

func (x *ChangeUserTestModulesRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ChangeUserTestModulesRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ChangeUserTestModulesRequest) GetTestModules() []string {
	if x != nil {
		return x.TestModules
	}
	return nil
}

// 修改用户测试模块响应
type ChangeUserTestModulesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          *User                  `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangeUserTestModulesResponse) Reset() {
	*x = ChangeUserTestModulesResponse{}
	mi := &file_api_proto_user_user_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangeUserTestModulesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeUserTestModulesResponse) ProtoMessage() {}

func (x *ChangeUserTestModulesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_user_user_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeUserTestModulesResponse.ProtoReflect.Descriptor instead.
func (*ChangeUserTestModulesResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_user_user_proto_rawDescGZIP(), []int{20}
}

func (x *ChangeUserTestModulesResponse) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

// 删除用户请求
type DeleteUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Username      string                 `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserRequest) Reset() {
	*x = DeleteUserRequest{}
	mi := &file_api_proto_user_user_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserRequest) ProtoMessage() {}

func (x *DeleteUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_user_user_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_user_user_proto_rawDescGZIP(), []int{21}
}

func (x *DeleteUserRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

// 删除用户响应
type DeleteUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserResponse) Reset() {
	*x = DeleteUserResponse{}
	mi := &file_api_proto_user_user_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserResponse) ProtoMessage() {}

func (x *DeleteUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_user_user_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserResponse.ProtoReflect.Descriptor instead.
func (*DeleteUserResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_user_user_proto_rawDescGZIP(), []int{22}
}

// 用户认证请求
type AuthenticateUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Password      string                 `protobuf:"bytes,1,opt,name=password,proto3" json:"password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AuthenticateUserRequest) Reset() {
	*x = AuthenticateUserRequest{}
	mi := &file_api_proto_user_user_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuthenticateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthenticateUserRequest) ProtoMessage() {}

func (x *AuthenticateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_user_user_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthenticateUserRequest.ProtoReflect.Descriptor instead.
func (*AuthenticateUserRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_user_user_proto_rawDescGZIP(), []int{23}
}

func (x *AuthenticateUserRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

// 用户认证响应
type AuthenticateUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AuthenticateUserResponse) Reset() {
	*x = AuthenticateUserResponse{}
	mi := &file_api_proto_user_user_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuthenticateUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthenticateUserResponse) ProtoMessage() {}

func (x *AuthenticateUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_user_user_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthenticateUserResponse.ProtoReflect.Descriptor instead.
func (*AuthenticateUserResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_user_user_proto_rawDescGZIP(), []int{24}
}

// 用户密码重置请求
type ResetUserPasswordRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RequestId     int64                  `protobuf:"varint,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	Username      string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResetUserPasswordRequest) Reset() {
	*x = ResetUserPasswordRequest{}
	mi := &file_api_proto_user_user_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResetUserPasswordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetUserPasswordRequest) ProtoMessage() {}

func (x *ResetUserPasswordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_user_user_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetUserPasswordRequest.ProtoReflect.Descriptor instead.
func (*ResetUserPasswordRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_user_user_proto_rawDescGZIP(), []int{25}
}

func (x *ResetUserPasswordRequest) GetRequestId() int64 {
	if x != nil {
		return x.RequestId
	}
	return 0
}

func (x *ResetUserPasswordRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

// 用户密码重置响应
type ResetUserPasswordResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResetUserPasswordResponse) Reset() {
	*x = ResetUserPasswordResponse{}
	mi := &file_api_proto_user_user_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResetUserPasswordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetUserPasswordResponse) ProtoMessage() {}

func (x *ResetUserPasswordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_user_user_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetUserPasswordResponse.ProtoReflect.Descriptor instead.
func (*ResetUserPasswordResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_user_user_proto_rawDescGZIP(), []int{26}
}

var File_api_proto_user_user_proto protoreflect.FileDescriptor

const file_api_proto_user_user_proto_rawDesc = "" +
	"\n" +
	"\x19api/proto/user/user.proto\x12\x04user\x1a\x1fgoogle/protobuf/timestamp.proto\"\xc6\x02\n" +
	"\x04User\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x03 \x01(\tR\bpassword\x12\x14\n" +
	"\x05email\x18\x04 \x01(\tR\x05email\x12\x1b\n" +
	"\tfull_name\x18\x05 \x01(\tR\bfullName\x12\x12\n" +
	"\x04role\x18\x06 \x01(\x05R\x04role\x12\x16\n" +
	"\x06status\x18\a \x01(\x05R\x06status\x12!\n" +
	"\ftest_modules\x18\b \x03(\tR\vtestModules\x129\n" +
	"\n" +
	"created_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\x17\n" +
	"\x15GetServiceInfoRequest\"|\n" +
	"\x16GetServiceInfoResponse\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x18\n" +
	"\aversion\x18\x02 \x01(\tR\aversion\x12\x12\n" +
	"\x04time\x18\x03 \x01(\tR\x04time\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\"`\n" +
	"\x0fRegisterRequest\x12\x1a\n" +
	"\busername\x18\x01 \x01(\tR\busername\x12\x14\n" +
	"\x05email\x18\x02 \x01(\tR\x05email\x12\x1b\n" +
	"\tfull_name\x18\x03 \x01(\tR\bfullName\"\x12\n" +
	"\x10RegisterResponse\"F\n" +
	"\fLoginRequest\x12\x1a\n" +
	"\busername\x18\x01 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\"E\n" +
	"\rLoginResponse\x12\x1e\n" +
	"\x04user\x18\x01 \x01(\v2\n" +
	".user.UserR\x04user\x12\x14\n" +
	"\x05token\x18\x02 \x01(\tR\x05token\"%\n" +
	"\rLogoutRequest\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\"\x10\n" +
	"\x0eLogoutResponse\"Z\n" +
	"\x0fGetUsersRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x16\n" +
	"\x06search\x18\x03 \x01(\tR\x06search\"4\n" +
	"\x10GetUsersResponse\x12 \n" +
	"\x05users\x18\x01 \x03(\v2\n" +
	".user.UserR\x05users\"r\n" +
	"\x11UpdateUserRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email\x12\x1b\n" +
	"\tfull_name\x18\x04 \x01(\tR\bfullName\"4\n" +
	"\x12UpdateUserResponse\x12\x1e\n" +
	"\x04user\x18\x01 \x01(\v2\n" +
	".user.UserR\x04user\"}\n" +
	"\x19ChangeUserPasswordRequest\x12\x1a\n" +
	"\busername\x18\x01 \x01(\tR\busername\x12!\n" +
	"\fold_password\x18\x02 \x01(\tR\voldPassword\x12!\n" +
	"\fnew_password\x18\x03 \x01(\tR\vnewPassword\"\x1c\n" +
	"\x1aChangeUserPasswordResponse\"W\n" +
	"\x15ChangeUserRoleRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x12\x12\n" +
	"\x04role\x18\x03 \x01(\x05R\x04role\"8\n" +
	"\x16ChangeUserRoleResponse\x12\x1e\n" +
	"\x04user\x18\x01 \x01(\v2\n" +
	".user.UserR\x04user\"]\n" +
	"\x17ChangeUserStatusRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x12\x16\n" +
	"\x06status\x18\x03 \x01(\x05R\x06status\":\n" +
	"\x18ChangeUserStatusResponse\x12\x1e\n" +
	"\x04user\x18\x01 \x01(\v2\n" +
	".user.UserR\x04user\"m\n" +
	"\x1cChangeUserTestModulesRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x12!\n" +
	"\ftest_modules\x18\x03 \x03(\tR\vtestModules\"?\n" +
	"\x1dChangeUserTestModulesResponse\x12\x1e\n" +
	"\x04user\x18\x01 \x01(\v2\n" +
	".user.UserR\x04user\"/\n" +
	"\x11DeleteUserRequest\x12\x1a\n" +
	"\busername\x18\x01 \x01(\tR\busername\"\x14\n" +
	"\x12DeleteUserResponse\"5\n" +
	"\x17AuthenticateUserRequest\x12\x1a\n" +
	"\bpassword\x18\x01 \x01(\tR\bpassword\"\x1a\n" +
	"\x18AuthenticateUserResponse\"U\n" +
	"\x18ResetUserPasswordRequest\x12\x1d\n" +
	"\n" +
	"request_id\x18\x01 \x01(\x03R\trequestId\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\"\x1b\n" +
	"\x19ResetUserPasswordResponse2\xbd\a\n" +
	"\vUserService\x12K\n" +
	"\x0eGetServiceInfo\x12\x1b.user.GetServiceInfoRequest\x1a\x1c.user.GetServiceInfoResponse\x129\n" +
	"\bRegister\x12\x15.user.RegisterRequest\x1a\x16.user.RegisterResponse\x120\n" +
	"\x05Login\x12\x12.user.LoginRequest\x1a\x13.user.LoginResponse\x123\n" +
	"\x06Logout\x12\x13.user.LogoutRequest\x1a\x14.user.LogoutResponse\x129\n" +
	"\bGetUsers\x12\x15.user.GetUsersRequest\x1a\x16.user.GetUsersResponse\x12?\n" +
	"\n" +
	"UpdateUser\x12\x17.user.UpdateUserRequest\x1a\x18.user.UpdateUserResponse\x12W\n" +
	"\x12ChangeUserPassword\x12\x1f.user.ChangeUserPasswordRequest\x1a .user.ChangeUserPasswordResponse\x12K\n" +
	"\x0eChangeUserRole\x12\x1b.user.ChangeUserRoleRequest\x1a\x1c.user.ChangeUserRoleResponse\x12Q\n" +
	"\x10ChangeUserStatus\x12\x1d.user.ChangeUserStatusRequest\x1a\x1e.user.ChangeUserStatusResponse\x12`\n" +
	"\x15ChangeUserTestModules\x12\".user.ChangeUserTestModulesRequest\x1a#.user.ChangeUserTestModulesResponse\x12?\n" +
	"\n" +
	"DeleteUser\x12\x17.user.DeleteUserRequest\x1a\x18.user.DeleteUserResponse\x12Q\n" +
	"\x10AuthenticateUser\x12\x1d.user.AuthenticateUserRequest\x1a\x1e.user.AuthenticateUserResponse\x12T\n" +
	"\x11ResetUserPassword\x12\x1e.user.ResetUserPasswordRequest\x1a\x1f.user.ResetUserPasswordResponseB%Z#thinta.test.platform/api/proto/userb\x06proto3"

var (
	file_api_proto_user_user_proto_rawDescOnce sync.Once
	file_api_proto_user_user_proto_rawDescData []byte
)

func file_api_proto_user_user_proto_rawDescGZIP() []byte {
	file_api_proto_user_user_proto_rawDescOnce.Do(func() {
		file_api_proto_user_user_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_proto_user_user_proto_rawDesc), len(file_api_proto_user_user_proto_rawDesc)))
	})
	return file_api_proto_user_user_proto_rawDescData
}

var file_api_proto_user_user_proto_msgTypes = make([]protoimpl.MessageInfo, 27)
var file_api_proto_user_user_proto_goTypes = []any{
	(*User)(nil),                          // 0: user.User
	(*GetServiceInfoRequest)(nil),         // 1: user.GetServiceInfoRequest
	(*GetServiceInfoResponse)(nil),        // 2: user.GetServiceInfoResponse
	(*RegisterRequest)(nil),               // 3: user.RegisterRequest
	(*RegisterResponse)(nil),              // 4: user.RegisterResponse
	(*LoginRequest)(nil),                  // 5: user.LoginRequest
	(*LoginResponse)(nil),                 // 6: user.LoginResponse
	(*LogoutRequest)(nil),                 // 7: user.LogoutRequest
	(*LogoutResponse)(nil),                // 8: user.LogoutResponse
	(*GetUsersRequest)(nil),               // 9: user.GetUsersRequest
	(*GetUsersResponse)(nil),              // 10: user.GetUsersResponse
	(*UpdateUserRequest)(nil),             // 11: user.UpdateUserRequest
	(*UpdateUserResponse)(nil),            // 12: user.UpdateUserResponse
	(*ChangeUserPasswordRequest)(nil),     // 13: user.ChangeUserPasswordRequest
	(*ChangeUserPasswordResponse)(nil),    // 14: user.ChangeUserPasswordResponse
	(*ChangeUserRoleRequest)(nil),         // 15: user.ChangeUserRoleRequest
	(*ChangeUserRoleResponse)(nil),        // 16: user.ChangeUserRoleResponse
	(*ChangeUserStatusRequest)(nil),       // 17: user.ChangeUserStatusRequest
	(*ChangeUserStatusResponse)(nil),      // 18: user.ChangeUserStatusResponse
	(*ChangeUserTestModulesRequest)(nil),  // 19: user.ChangeUserTestModulesRequest
	(*ChangeUserTestModulesResponse)(nil), // 20: user.ChangeUserTestModulesResponse
	(*DeleteUserRequest)(nil),             // 21: user.DeleteUserRequest
	(*DeleteUserResponse)(nil),            // 22: user.DeleteUserResponse
	(*AuthenticateUserRequest)(nil),       // 23: user.AuthenticateUserRequest
	(*AuthenticateUserResponse)(nil),      // 24: user.AuthenticateUserResponse
	(*ResetUserPasswordRequest)(nil),      // 25: user.ResetUserPasswordRequest
	(*ResetUserPasswordResponse)(nil),     // 26: user.ResetUserPasswordResponse
	(*timestamp.Timestamp)(nil),           // 27: google.protobuf.Timestamp
}
var file_api_proto_user_user_proto_depIdxs = []int32{
	27, // 0: user.User.created_at:type_name -> google.protobuf.Timestamp
	27, // 1: user.User.updated_at:type_name -> google.protobuf.Timestamp
	0,  // 2: user.LoginResponse.user:type_name -> user.User
	0,  // 3: user.GetUsersResponse.users:type_name -> user.User
	0,  // 4: user.UpdateUserResponse.user:type_name -> user.User
	0,  // 5: user.ChangeUserRoleResponse.user:type_name -> user.User
	0,  // 6: user.ChangeUserStatusResponse.user:type_name -> user.User
	0,  // 7: user.ChangeUserTestModulesResponse.user:type_name -> user.User
	1,  // 8: user.UserService.GetServiceInfo:input_type -> user.GetServiceInfoRequest
	3,  // 9: user.UserService.Register:input_type -> user.RegisterRequest
	5,  // 10: user.UserService.Login:input_type -> user.LoginRequest
	7,  // 11: user.UserService.Logout:input_type -> user.LogoutRequest
	9,  // 12: user.UserService.GetUsers:input_type -> user.GetUsersRequest
	11, // 13: user.UserService.UpdateUser:input_type -> user.UpdateUserRequest
	13, // 14: user.UserService.ChangeUserPassword:input_type -> user.ChangeUserPasswordRequest
	15, // 15: user.UserService.ChangeUserRole:input_type -> user.ChangeUserRoleRequest
	17, // 16: user.UserService.ChangeUserStatus:input_type -> user.ChangeUserStatusRequest
	19, // 17: user.UserService.ChangeUserTestModules:input_type -> user.ChangeUserTestModulesRequest
	21, // 18: user.UserService.DeleteUser:input_type -> user.DeleteUserRequest
	23, // 19: user.UserService.AuthenticateUser:input_type -> user.AuthenticateUserRequest
	25, // 20: user.UserService.ResetUserPassword:input_type -> user.ResetUserPasswordRequest
	2,  // 21: user.UserService.GetServiceInfo:output_type -> user.GetServiceInfoResponse
	4,  // 22: user.UserService.Register:output_type -> user.RegisterResponse
	6,  // 23: user.UserService.Login:output_type -> user.LoginResponse
	8,  // 24: user.UserService.Logout:output_type -> user.LogoutResponse
	10, // 25: user.UserService.GetUsers:output_type -> user.GetUsersResponse
	12, // 26: user.UserService.UpdateUser:output_type -> user.UpdateUserResponse
	14, // 27: user.UserService.ChangeUserPassword:output_type -> user.ChangeUserPasswordResponse
	16, // 28: user.UserService.ChangeUserRole:output_type -> user.ChangeUserRoleResponse
	18, // 29: user.UserService.ChangeUserStatus:output_type -> user.ChangeUserStatusResponse
	20, // 30: user.UserService.ChangeUserTestModules:output_type -> user.ChangeUserTestModulesResponse
	22, // 31: user.UserService.DeleteUser:output_type -> user.DeleteUserResponse
	24, // 32: user.UserService.AuthenticateUser:output_type -> user.AuthenticateUserResponse
	26, // 33: user.UserService.ResetUserPassword:output_type -> user.ResetUserPasswordResponse
	21, // [21:34] is the sub-list for method output_type
	8,  // [8:21] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_api_proto_user_user_proto_init() }
func file_api_proto_user_user_proto_init() {
	if File_api_proto_user_user_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_proto_user_user_proto_rawDesc), len(file_api_proto_user_user_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   27,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_proto_user_user_proto_goTypes,
		DependencyIndexes: file_api_proto_user_user_proto_depIdxs,
		MessageInfos:      file_api_proto_user_user_proto_msgTypes,
	}.Build()
	File_api_proto_user_user_proto = out.File
	file_api_proto_user_user_proto_goTypes = nil
	file_api_proto_user_user_proto_depIdxs = nil
}
