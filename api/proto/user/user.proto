syntax = "proto3";

package user;

option go_package = "thinta.test.platform/api/proto/user";

import "google/protobuf/timestamp.proto";

// 用户服务定义
service UserService {
  // 获取服务信息
  rpc GetServiceInfo(GetServiceInfoRequest) returns (GetServiceInfoResponse);
  // 注册
  rpc Register(RegisterRequest) returns (RegisterResponse);
  // 登录
  rpc Login(LoginRequest) returns (LoginResponse);
  // 登出
  rpc Logout(LogoutRequest) returns (LogoutResponse);
  // 获取用户列表
  rpc GetUsers(GetUsersRequest) returns (GetUsersResponse);
  // 更新用户
  rpc UpdateUser(UpdateUserRequest) returns (UpdateUserResponse);
  // 修改密码
  rpc ChangeUserPassword(ChangeUserPasswordRequest) returns (ChangeUserPasswordResponse);
  // 修改用户角色
  rpc ChangeUserRole(ChangeUserRoleRequest) returns (ChangeUserRoleResponse);
  // 修改用户状态
  rpc ChangeUserStatus(ChangeUserStatusRequest) returns (ChangeUserStatusResponse);
  // 修改用户测试模块
  rpc ChangeUserTestModules(ChangeUserTestModulesRequest) returns (ChangeUserTestModulesResponse);
  // 删除用户
  rpc DeleteUser(DeleteUserRequest) returns (DeleteUserResponse);
  // 用户认证
  rpc AuthenticateUser(AuthenticateUserRequest) returns (AuthenticateUserResponse);
  // 用户密码重置
  rpc ResetUserPassword(ResetUserPasswordRequest) returns (ResetUserPasswordResponse);
}

// 用户信息
message User {
  int64 id = 1;
  string username = 2;
  string password = 3;
  string email = 4;
  string full_name = 5;
  int32 role = 6;
  int32 status = 7;
  repeated string test_modules = 8;
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;
}

// 获取服务信息请求
message GetServiceInfoRequest {}

// 获取服务信息响应
message GetServiceInfoResponse {
  string name = 1;
  string version = 2;
  string time = 3;
  string description = 4;
}

// 注册请求
message RegisterRequest {
  string username = 1;
  string email = 2;
  string full_name = 3;
}

// 注册响应
message RegisterResponse {}

// 登录请求
message LoginRequest {
  string username = 1;
  string password = 2;
}

// 登录响应
message LoginResponse {
  User user = 1;
  string token = 2;
}

// 登出请求
message LogoutRequest {
  string token = 1;
}

// 登出响应
message LogoutResponse {}

// 获取用户列表请求
message GetUsersRequest {
  int32 page = 1;
  int32 page_size = 2;
  string search = 3;
}

// 获取用户列表响应
message GetUsersResponse {
  repeated User users = 1;
}

// 更新用户请求
message UpdateUserRequest {
  int64 id = 1;
  string username = 2;
  string email = 3;
  string full_name = 4;
}

// 更新用户响应
message UpdateUserResponse {
  User user = 1;
}

// 修改密码请求
message ChangeUserPasswordRequest {
  string username = 1;
  string old_password = 2;
  string new_password = 3;
}

// 修改密码响应
message ChangeUserPasswordResponse {}

// 修改用户角色
message ChangeUserRoleRequest {
  int64 id = 1;
  string username = 2;
  int32 role = 3;
}

// 修改用户角色响应
message ChangeUserRoleResponse {
  User user = 1;
}

// 修改用户状态
message ChangeUserStatusRequest {
  int64 id = 1;
  string username = 2;
  int32 status = 3;
}

// 修改用户状态响应
message ChangeUserStatusResponse {
  User user = 1;
}

// 修改用户测试模块
message ChangeUserTestModulesRequest {
  int64 id = 1;
  string username = 2;
  repeated string test_modules = 3;
}

// 修改用户测试模块响应
message ChangeUserTestModulesResponse {
  User user = 1;
}

// 删除用户请求
message DeleteUserRequest {
  string username = 1;
}

// 删除用户响应
message DeleteUserResponse {}

// 用户认证请求
message AuthenticateUserRequest {
  string password = 1;
}

// 用户认证响应
message AuthenticateUserResponse {}

// 用户密码重置请求
message ResetUserPasswordRequest {
  int64 request_id = 1;
  string username = 2;
}

// 用户密码重置响应
message ResetUserPasswordResponse {}
