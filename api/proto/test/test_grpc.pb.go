// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.12.4
// source: api/proto/test/test.proto

package test

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	TestService_GetServiceInfo_FullMethodName         = "/test.TestService/GetServiceInfo"
	TestService_CreateTestModule_FullMethodName       = "/test.TestService/CreateTestModule"
	TestService_GetTestModule_FullMethodName          = "/test.TestService/GetTestModule"
	TestService_UpdateTestModule_FullMethodName       = "/test.TestService/UpdateTestModule"
	TestService_DeleteTestModule_FullMethodName       = "/test.TestService/DeleteTestModule"
	TestService_ListTestModules_FullMethodName        = "/test.TestService/ListTestModules"
	TestService_DownloadTestModule_FullMethodName     = "/test.TestService/DownloadTestModule"
	TestService_UploadTestModule_FullMethodName       = "/test.TestService/UploadTestModule"
	TestService_ApplyTestModule_FullMethodName        = "/test.TestService/ApplyTestModule"
	TestService_ListTestModuleApplies_FullMethodName  = "/test.TestService/ListTestModuleApplies"
	TestService_ProcessTestModuleApply_FullMethodName = "/test.TestService/ProcessTestModuleApply"
	TestService_CreateTestRecord_FullMethodName       = "/test.TestService/CreateTestRecord"
	TestService_GetTestRecord_FullMethodName          = "/test.TestService/GetTestRecord"
	TestService_DeleteTestRecord_FullMethodName       = "/test.TestService/DeleteTestRecord"
	TestService_ListTestRecords_FullMethodName        = "/test.TestService/ListTestRecords"
	TestService_CreateTestExecution_FullMethodName    = "/test.TestService/CreateTestExecution"
	TestService_GetTestExecution_FullMethodName       = "/test.TestService/GetTestExecution"
	TestService_ListTestExecutions_FullMethodName     = "/test.TestService/ListTestExecutions"
)

// TestServiceClient is the client API for TestService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 测试服务定义
type TestServiceClient interface {
	// 获取服务信息
	GetServiceInfo(ctx context.Context, in *GetServiceInfoRequest, opts ...grpc.CallOption) (*GetServiceInfoResponse, error)
	// 创建测试模块
	CreateTestModule(ctx context.Context, in *CreateTestModuleRequest, opts ...grpc.CallOption) (*CreateTestModuleResponse, error)
	// 获取测试模块
	GetTestModule(ctx context.Context, in *GetTestModuleRequest, opts ...grpc.CallOption) (*GetTestModuleResponse, error)
	// 更新测试模块
	UpdateTestModule(ctx context.Context, in *UpdateTestModuleRequest, opts ...grpc.CallOption) (*UpdateTestModuleResponse, error)
	// 删除测试模块
	DeleteTestModule(ctx context.Context, in *DeleteTestModuleRequest, opts ...grpc.CallOption) (*DeleteTestModuleResponse, error)
	// 测试模块列表
	ListTestModules(ctx context.Context, in *ListTestModulesRequest, opts ...grpc.CallOption) (*ListTestModulesResponse, error)
	// 下载测试模块
	DownloadTestModule(ctx context.Context, in *DownloadTestModuleRequest, opts ...grpc.CallOption) (*DownloadTestModuleResponse, error)
	// 上传测试模块
	UploadTestModule(ctx context.Context, in *UploadTestModuleRequest, opts ...grpc.CallOption) (*UploadTestModuleResponse, error)
	// 测试模块申请
	ApplyTestModule(ctx context.Context, in *ApplyTestModuleRequest, opts ...grpc.CallOption) (*ApplyTestModuleResponse, error)
	// 测试模块申请列表
	ListTestModuleApplies(ctx context.Context, in *ListTestModuleAppliesRequest, opts ...grpc.CallOption) (*ListTestModuleAppliesResponse, error)
	// 测试模块申请处理
	ProcessTestModuleApply(ctx context.Context, in *ProcessTestModuleApplyRequest, opts ...grpc.CallOption) (*ProcessTestModuleApplyResponse, error)
	// 创建测试记录
	CreateTestRecord(ctx context.Context, in *CreateTestRecordRequest, opts ...grpc.CallOption) (*CreateTestRecordResponse, error)
	// 获取测试记录
	GetTestRecord(ctx context.Context, in *GetTestRecordRequest, opts ...grpc.CallOption) (*GetTestRecordResponse, error)
	// 删除测试记录
	DeleteTestRecord(ctx context.Context, in *DeleteTestRecordRequest, opts ...grpc.CallOption) (*DeleteTestRecordResponse, error)
	// 测试记录列表
	ListTestRecords(ctx context.Context, in *ListTestRecordsRequest, opts ...grpc.CallOption) (*ListTestRecordsResponse, error)
	// 创建测试执行记录
	CreateTestExecution(ctx context.Context, in *CreateTestExecutionRequest, opts ...grpc.CallOption) (*CreateTestExecutionResponse, error)
	// 获取测试执行记录
	GetTestExecution(ctx context.Context, in *GetTestExecutionRequest, opts ...grpc.CallOption) (*GetTestExecutionResponse, error)
	// 测试执行记录列表
	ListTestExecutions(ctx context.Context, in *ListTestExecutionsRequest, opts ...grpc.CallOption) (*ListTestExecutionsResponse, error)
}

type testServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTestServiceClient(cc grpc.ClientConnInterface) TestServiceClient {
	return &testServiceClient{cc}
}

func (c *testServiceClient) GetServiceInfo(ctx context.Context, in *GetServiceInfoRequest, opts ...grpc.CallOption) (*GetServiceInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetServiceInfoResponse)
	err := c.cc.Invoke(ctx, TestService_GetServiceInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *testServiceClient) CreateTestModule(ctx context.Context, in *CreateTestModuleRequest, opts ...grpc.CallOption) (*CreateTestModuleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateTestModuleResponse)
	err := c.cc.Invoke(ctx, TestService_CreateTestModule_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *testServiceClient) GetTestModule(ctx context.Context, in *GetTestModuleRequest, opts ...grpc.CallOption) (*GetTestModuleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTestModuleResponse)
	err := c.cc.Invoke(ctx, TestService_GetTestModule_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *testServiceClient) UpdateTestModule(ctx context.Context, in *UpdateTestModuleRequest, opts ...grpc.CallOption) (*UpdateTestModuleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateTestModuleResponse)
	err := c.cc.Invoke(ctx, TestService_UpdateTestModule_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *testServiceClient) DeleteTestModule(ctx context.Context, in *DeleteTestModuleRequest, opts ...grpc.CallOption) (*DeleteTestModuleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteTestModuleResponse)
	err := c.cc.Invoke(ctx, TestService_DeleteTestModule_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *testServiceClient) ListTestModules(ctx context.Context, in *ListTestModulesRequest, opts ...grpc.CallOption) (*ListTestModulesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListTestModulesResponse)
	err := c.cc.Invoke(ctx, TestService_ListTestModules_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *testServiceClient) DownloadTestModule(ctx context.Context, in *DownloadTestModuleRequest, opts ...grpc.CallOption) (*DownloadTestModuleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DownloadTestModuleResponse)
	err := c.cc.Invoke(ctx, TestService_DownloadTestModule_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *testServiceClient) UploadTestModule(ctx context.Context, in *UploadTestModuleRequest, opts ...grpc.CallOption) (*UploadTestModuleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UploadTestModuleResponse)
	err := c.cc.Invoke(ctx, TestService_UploadTestModule_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *testServiceClient) ApplyTestModule(ctx context.Context, in *ApplyTestModuleRequest, opts ...grpc.CallOption) (*ApplyTestModuleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ApplyTestModuleResponse)
	err := c.cc.Invoke(ctx, TestService_ApplyTestModule_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *testServiceClient) ListTestModuleApplies(ctx context.Context, in *ListTestModuleAppliesRequest, opts ...grpc.CallOption) (*ListTestModuleAppliesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListTestModuleAppliesResponse)
	err := c.cc.Invoke(ctx, TestService_ListTestModuleApplies_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *testServiceClient) ProcessTestModuleApply(ctx context.Context, in *ProcessTestModuleApplyRequest, opts ...grpc.CallOption) (*ProcessTestModuleApplyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ProcessTestModuleApplyResponse)
	err := c.cc.Invoke(ctx, TestService_ProcessTestModuleApply_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *testServiceClient) CreateTestRecord(ctx context.Context, in *CreateTestRecordRequest, opts ...grpc.CallOption) (*CreateTestRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateTestRecordResponse)
	err := c.cc.Invoke(ctx, TestService_CreateTestRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *testServiceClient) GetTestRecord(ctx context.Context, in *GetTestRecordRequest, opts ...grpc.CallOption) (*GetTestRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTestRecordResponse)
	err := c.cc.Invoke(ctx, TestService_GetTestRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *testServiceClient) DeleteTestRecord(ctx context.Context, in *DeleteTestRecordRequest, opts ...grpc.CallOption) (*DeleteTestRecordResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteTestRecordResponse)
	err := c.cc.Invoke(ctx, TestService_DeleteTestRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *testServiceClient) ListTestRecords(ctx context.Context, in *ListTestRecordsRequest, opts ...grpc.CallOption) (*ListTestRecordsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListTestRecordsResponse)
	err := c.cc.Invoke(ctx, TestService_ListTestRecords_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *testServiceClient) CreateTestExecution(ctx context.Context, in *CreateTestExecutionRequest, opts ...grpc.CallOption) (*CreateTestExecutionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateTestExecutionResponse)
	err := c.cc.Invoke(ctx, TestService_CreateTestExecution_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *testServiceClient) GetTestExecution(ctx context.Context, in *GetTestExecutionRequest, opts ...grpc.CallOption) (*GetTestExecutionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTestExecutionResponse)
	err := c.cc.Invoke(ctx, TestService_GetTestExecution_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *testServiceClient) ListTestExecutions(ctx context.Context, in *ListTestExecutionsRequest, opts ...grpc.CallOption) (*ListTestExecutionsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListTestExecutionsResponse)
	err := c.cc.Invoke(ctx, TestService_ListTestExecutions_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TestServiceServer is the server API for TestService service.
// All implementations must embed UnimplementedTestServiceServer
// for forward compatibility.
//
// 测试服务定义
type TestServiceServer interface {
	// 获取服务信息
	GetServiceInfo(context.Context, *GetServiceInfoRequest) (*GetServiceInfoResponse, error)
	// 创建测试模块
	CreateTestModule(context.Context, *CreateTestModuleRequest) (*CreateTestModuleResponse, error)
	// 获取测试模块
	GetTestModule(context.Context, *GetTestModuleRequest) (*GetTestModuleResponse, error)
	// 更新测试模块
	UpdateTestModule(context.Context, *UpdateTestModuleRequest) (*UpdateTestModuleResponse, error)
	// 删除测试模块
	DeleteTestModule(context.Context, *DeleteTestModuleRequest) (*DeleteTestModuleResponse, error)
	// 测试模块列表
	ListTestModules(context.Context, *ListTestModulesRequest) (*ListTestModulesResponse, error)
	// 下载测试模块
	DownloadTestModule(context.Context, *DownloadTestModuleRequest) (*DownloadTestModuleResponse, error)
	// 上传测试模块
	UploadTestModule(context.Context, *UploadTestModuleRequest) (*UploadTestModuleResponse, error)
	// 测试模块申请
	ApplyTestModule(context.Context, *ApplyTestModuleRequest) (*ApplyTestModuleResponse, error)
	// 测试模块申请列表
	ListTestModuleApplies(context.Context, *ListTestModuleAppliesRequest) (*ListTestModuleAppliesResponse, error)
	// 测试模块申请处理
	ProcessTestModuleApply(context.Context, *ProcessTestModuleApplyRequest) (*ProcessTestModuleApplyResponse, error)
	// 创建测试记录
	CreateTestRecord(context.Context, *CreateTestRecordRequest) (*CreateTestRecordResponse, error)
	// 获取测试记录
	GetTestRecord(context.Context, *GetTestRecordRequest) (*GetTestRecordResponse, error)
	// 删除测试记录
	DeleteTestRecord(context.Context, *DeleteTestRecordRequest) (*DeleteTestRecordResponse, error)
	// 测试记录列表
	ListTestRecords(context.Context, *ListTestRecordsRequest) (*ListTestRecordsResponse, error)
	// 创建测试执行记录
	CreateTestExecution(context.Context, *CreateTestExecutionRequest) (*CreateTestExecutionResponse, error)
	// 获取测试执行记录
	GetTestExecution(context.Context, *GetTestExecutionRequest) (*GetTestExecutionResponse, error)
	// 测试执行记录列表
	ListTestExecutions(context.Context, *ListTestExecutionsRequest) (*ListTestExecutionsResponse, error)
	mustEmbedUnimplementedTestServiceServer()
}

// UnimplementedTestServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTestServiceServer struct{}

func (UnimplementedTestServiceServer) GetServiceInfo(context.Context, *GetServiceInfoRequest) (*GetServiceInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceInfo not implemented")
}
func (UnimplementedTestServiceServer) CreateTestModule(context.Context, *CreateTestModuleRequest) (*CreateTestModuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTestModule not implemented")
}
func (UnimplementedTestServiceServer) GetTestModule(context.Context, *GetTestModuleRequest) (*GetTestModuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTestModule not implemented")
}
func (UnimplementedTestServiceServer) UpdateTestModule(context.Context, *UpdateTestModuleRequest) (*UpdateTestModuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTestModule not implemented")
}
func (UnimplementedTestServiceServer) DeleteTestModule(context.Context, *DeleteTestModuleRequest) (*DeleteTestModuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTestModule not implemented")
}
func (UnimplementedTestServiceServer) ListTestModules(context.Context, *ListTestModulesRequest) (*ListTestModulesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTestModules not implemented")
}
func (UnimplementedTestServiceServer) DownloadTestModule(context.Context, *DownloadTestModuleRequest) (*DownloadTestModuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DownloadTestModule not implemented")
}
func (UnimplementedTestServiceServer) UploadTestModule(context.Context, *UploadTestModuleRequest) (*UploadTestModuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadTestModule not implemented")
}
func (UnimplementedTestServiceServer) ApplyTestModule(context.Context, *ApplyTestModuleRequest) (*ApplyTestModuleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApplyTestModule not implemented")
}
func (UnimplementedTestServiceServer) ListTestModuleApplies(context.Context, *ListTestModuleAppliesRequest) (*ListTestModuleAppliesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTestModuleApplies not implemented")
}
func (UnimplementedTestServiceServer) ProcessTestModuleApply(context.Context, *ProcessTestModuleApplyRequest) (*ProcessTestModuleApplyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessTestModuleApply not implemented")
}
func (UnimplementedTestServiceServer) CreateTestRecord(context.Context, *CreateTestRecordRequest) (*CreateTestRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTestRecord not implemented")
}
func (UnimplementedTestServiceServer) GetTestRecord(context.Context, *GetTestRecordRequest) (*GetTestRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTestRecord not implemented")
}
func (UnimplementedTestServiceServer) DeleteTestRecord(context.Context, *DeleteTestRecordRequest) (*DeleteTestRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTestRecord not implemented")
}
func (UnimplementedTestServiceServer) ListTestRecords(context.Context, *ListTestRecordsRequest) (*ListTestRecordsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTestRecords not implemented")
}
func (UnimplementedTestServiceServer) CreateTestExecution(context.Context, *CreateTestExecutionRequest) (*CreateTestExecutionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTestExecution not implemented")
}
func (UnimplementedTestServiceServer) GetTestExecution(context.Context, *GetTestExecutionRequest) (*GetTestExecutionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTestExecution not implemented")
}
func (UnimplementedTestServiceServer) ListTestExecutions(context.Context, *ListTestExecutionsRequest) (*ListTestExecutionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTestExecutions not implemented")
}
func (UnimplementedTestServiceServer) mustEmbedUnimplementedTestServiceServer() {}
func (UnimplementedTestServiceServer) testEmbeddedByValue()                     {}

// UnsafeTestServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TestServiceServer will
// result in compilation errors.
type UnsafeTestServiceServer interface {
	mustEmbedUnimplementedTestServiceServer()
}

func RegisterTestServiceServer(s grpc.ServiceRegistrar, srv TestServiceServer) {
	// If the following call pancis, it indicates UnimplementedTestServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TestService_ServiceDesc, srv)
}

func _TestService_GetServiceInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServiceInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestServiceServer).GetServiceInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TestService_GetServiceInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestServiceServer).GetServiceInfo(ctx, req.(*GetServiceInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TestService_CreateTestModule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTestModuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestServiceServer).CreateTestModule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TestService_CreateTestModule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestServiceServer).CreateTestModule(ctx, req.(*CreateTestModuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TestService_GetTestModule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTestModuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestServiceServer).GetTestModule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TestService_GetTestModule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestServiceServer).GetTestModule(ctx, req.(*GetTestModuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TestService_UpdateTestModule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTestModuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestServiceServer).UpdateTestModule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TestService_UpdateTestModule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestServiceServer).UpdateTestModule(ctx, req.(*UpdateTestModuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TestService_DeleteTestModule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTestModuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestServiceServer).DeleteTestModule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TestService_DeleteTestModule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestServiceServer).DeleteTestModule(ctx, req.(*DeleteTestModuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TestService_ListTestModules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTestModulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestServiceServer).ListTestModules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TestService_ListTestModules_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestServiceServer).ListTestModules(ctx, req.(*ListTestModulesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TestService_DownloadTestModule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DownloadTestModuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestServiceServer).DownloadTestModule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TestService_DownloadTestModule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestServiceServer).DownloadTestModule(ctx, req.(*DownloadTestModuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TestService_UploadTestModule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadTestModuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestServiceServer).UploadTestModule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TestService_UploadTestModule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestServiceServer).UploadTestModule(ctx, req.(*UploadTestModuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TestService_ApplyTestModule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplyTestModuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestServiceServer).ApplyTestModule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TestService_ApplyTestModule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestServiceServer).ApplyTestModule(ctx, req.(*ApplyTestModuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TestService_ListTestModuleApplies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTestModuleAppliesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestServiceServer).ListTestModuleApplies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TestService_ListTestModuleApplies_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestServiceServer).ListTestModuleApplies(ctx, req.(*ListTestModuleAppliesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TestService_ProcessTestModuleApply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessTestModuleApplyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestServiceServer).ProcessTestModuleApply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TestService_ProcessTestModuleApply_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestServiceServer).ProcessTestModuleApply(ctx, req.(*ProcessTestModuleApplyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TestService_CreateTestRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTestRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestServiceServer).CreateTestRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TestService_CreateTestRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestServiceServer).CreateTestRecord(ctx, req.(*CreateTestRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TestService_GetTestRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTestRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestServiceServer).GetTestRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TestService_GetTestRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestServiceServer).GetTestRecord(ctx, req.(*GetTestRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TestService_DeleteTestRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTestRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestServiceServer).DeleteTestRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TestService_DeleteTestRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestServiceServer).DeleteTestRecord(ctx, req.(*DeleteTestRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TestService_ListTestRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTestRecordsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestServiceServer).ListTestRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TestService_ListTestRecords_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestServiceServer).ListTestRecords(ctx, req.(*ListTestRecordsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TestService_CreateTestExecution_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTestExecutionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestServiceServer).CreateTestExecution(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TestService_CreateTestExecution_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestServiceServer).CreateTestExecution(ctx, req.(*CreateTestExecutionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TestService_GetTestExecution_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTestExecutionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestServiceServer).GetTestExecution(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TestService_GetTestExecution_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestServiceServer).GetTestExecution(ctx, req.(*GetTestExecutionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TestService_ListTestExecutions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTestExecutionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestServiceServer).ListTestExecutions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TestService_ListTestExecutions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestServiceServer).ListTestExecutions(ctx, req.(*ListTestExecutionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TestService_ServiceDesc is the grpc.ServiceDesc for TestService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TestService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "test.TestService",
	HandlerType: (*TestServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetServiceInfo",
			Handler:    _TestService_GetServiceInfo_Handler,
		},
		{
			MethodName: "CreateTestModule",
			Handler:    _TestService_CreateTestModule_Handler,
		},
		{
			MethodName: "GetTestModule",
			Handler:    _TestService_GetTestModule_Handler,
		},
		{
			MethodName: "UpdateTestModule",
			Handler:    _TestService_UpdateTestModule_Handler,
		},
		{
			MethodName: "DeleteTestModule",
			Handler:    _TestService_DeleteTestModule_Handler,
		},
		{
			MethodName: "ListTestModules",
			Handler:    _TestService_ListTestModules_Handler,
		},
		{
			MethodName: "DownloadTestModule",
			Handler:    _TestService_DownloadTestModule_Handler,
		},
		{
			MethodName: "UploadTestModule",
			Handler:    _TestService_UploadTestModule_Handler,
		},
		{
			MethodName: "ApplyTestModule",
			Handler:    _TestService_ApplyTestModule_Handler,
		},
		{
			MethodName: "ListTestModuleApplies",
			Handler:    _TestService_ListTestModuleApplies_Handler,
		},
		{
			MethodName: "ProcessTestModuleApply",
			Handler:    _TestService_ProcessTestModuleApply_Handler,
		},
		{
			MethodName: "CreateTestRecord",
			Handler:    _TestService_CreateTestRecord_Handler,
		},
		{
			MethodName: "GetTestRecord",
			Handler:    _TestService_GetTestRecord_Handler,
		},
		{
			MethodName: "DeleteTestRecord",
			Handler:    _TestService_DeleteTestRecord_Handler,
		},
		{
			MethodName: "ListTestRecords",
			Handler:    _TestService_ListTestRecords_Handler,
		},
		{
			MethodName: "CreateTestExecution",
			Handler:    _TestService_CreateTestExecution_Handler,
		},
		{
			MethodName: "GetTestExecution",
			Handler:    _TestService_GetTestExecution_Handler,
		},
		{
			MethodName: "ListTestExecutions",
			Handler:    _TestService_ListTestExecutions_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/proto/test/test.proto",
}
