syntax = "proto3";

package test;

option go_package = "github.com/testplatform/api/proto/test";

import "google/protobuf/timestamp.proto";

message KeyValuePair {
  map<string, string> values = 1;
}

// 测试服务定义
service TestService {
  // 获取服务信息
  rpc GetServiceInfo(GetServiceInfoRequest) returns (GetServiceInfoResponse);
  // 创建测试模块
  rpc CreateTestModule(CreateTestModuleRequest) returns (CreateTestModuleResponse);
  // 获取测试模块
  rpc GetTestModule(GetTestModuleRequest) returns (GetTestModuleResponse);
  // 更新测试模块
  rpc UpdateTestModule(UpdateTestModuleRequest) returns (UpdateTestModuleResponse);
  // 删除测试模块
  rpc DeleteTestModule(DeleteTestModuleRequest) returns (DeleteTestModuleResponse);
  // 测试模块列表
  rpc ListTestModules(ListTestModulesRequest) returns (ListTestModulesResponse);
  // 下载测试模块
  rpc DownloadTestModule(DownloadTestModuleRequest) returns (DownloadTestModuleResponse);
  // 上传测试模块
  rpc UploadTestModule(UploadTestModuleRequest) returns (UploadTestModuleResponse);
  // 测试模块申请
  rpc ApplyTestModule(ApplyTestModuleRequest) returns (ApplyTestModuleResponse);
  // 测试模块申请列表
  rpc ListTestModuleApplies(ListTestModuleAppliesRequest) returns (ListTestModuleAppliesResponse);
  // 测试模块申请处理
  rpc ProcessTestModuleApply(ProcessTestModuleApplyRequest) returns (ProcessTestModuleApplyResponse);
  // 创建测试记录
  rpc CreateTestRecord(CreateTestRecordRequest) returns (CreateTestRecordResponse);
  // 获取测试记录
  rpc GetTestRecord(GetTestRecordRequest) returns (GetTestRecordResponse);
  // 删除测试记录
  rpc DeleteTestRecord(DeleteTestRecordRequest) returns (DeleteTestRecordResponse);
  // 测试记录列表
  rpc ListTestRecords(ListTestRecordsRequest) returns (ListTestRecordsResponse);
  // 创建测试执行记录
  rpc CreateTestExecution(CreateTestExecutionRequest) returns (CreateTestExecutionResponse);
  // 获取测试执行记录
  rpc GetTestExecution(GetTestExecutionRequest) returns (GetTestExecutionResponse);
  // 测试执行记录列表
  rpc ListTestExecutions(ListTestExecutionsRequest) returns (ListTestExecutionsResponse);
}


// 获取服务信息请求
message GetServiceInfoRequest {}

// 获取服务信息响应
message GetServiceInfoResponse {
  string name = 1;
  string version = 2;
  string time = 3;
  string description = 4;
}

// 测试用例状态
enum TestStatus {
  PENDING = 0;
  RUNNING = 1;
  PASSED = 2;
  FAILED = 3;
  SKIPPED = 4;
}

// 测试模块
message TestModule {
  int64 id = 1;
  string name = 2;
  string plugin = 3;
  string version = 4;
  map<string, KeyValuePair> itmes = 5;
  string description = 6;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
}

// 测试模块申请
message TestModuleApply {
  int64 id = 1;
  int64 request_by = 2;
  repeated string modules = 3;
  int32 approved = 4;
  google.protobuf.Timestamp created_at = 5;
}

// 测试记录模型
message TestRecord {
  int64 id = 1;
  string RecordNumber = 2;
  string description = 3;
  string module = 4;
  string result = 5;
  int64 created_by = 6;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
}

// 测试执行记录
message TestExecution {
  int64 id = 1;
  int64 test_id = 2;
  TestStatus status = 3;
  string result = 4;
  string error_message = 5;
  int64 duration_ms = 6;
  int64 executed_by = 7;
  string environment = 8;
  google.protobuf.Timestamp started_at = 9;
  google.protobuf.Timestamp finished_at = 10;
  google.protobuf.Timestamp created_at = 11;
  google.protobuf.Timestamp updated_at = 12;
}


// 创建测试模块请求
message CreateTestModuleRequest {
  string name = 1;
  string plugin = 2;
  string version = 3;
  map<string, KeyValuePair> items = 4;
  string description = 5;
}

// 创建测试模块响应
message CreateTestModuleResponse {
  TestModule test_module = 1;
}

// 获取测试模块请求
message GetTestModuleRequest {
  string name = 1;
}

// 获取测试模块响应
message GetTestModuleResponse {
  TestModule test_module = 1;
}

// 更新测试模块请求
message UpdateTestModuleRequest {
  string name = 2;
  string plugin = 3;
  string version = 4;
  map<string, KeyValuePair> items = 5;
  string description = 6;
}

// 更新测试模块响应
message UpdateTestModuleResponse {
  TestModule test_module = 1;
}

// 删除测试模块请求
message DeleteTestModuleRequest {
  string name = 1;
}

// 删除测试模块响应
message DeleteTestModuleResponse {
  bool success = 1;
}

// 测试模块列表请求
message ListTestModulesRequest {
  int32 page = 1;
  int32 page_size = 2;
  string search = 3;
}

// 测试模块列表响应
message ListTestModulesResponse {
  repeated TestModule test_modules = 1;
  int64 total = 2;
  int32 page = 3;
  int32 page_size = 4;
}

// 下载测试模块请求
message DownloadTestModuleRequest {
  string name = 1;
}

// 下载测试模块响应
message DownloadTestModuleResponse {
  bytes content = 1;
}

// 上传测试模块请求
message UploadTestModuleRequest {
  string name = 1;
  string plugin = 2;
  string version = 3;
  map<string, KeyValuePair> items = 4;
  string description = 5;
  bytes content = 6;
}

// 上传测试模块响应
message UploadTestModuleResponse {
  TestModule test_module = 1;
}


// 测试模块申请请求
message ApplyTestModuleRequest {
  int64 id = 1;
  int64 request_by = 2;
  repeated string modules = 3;
}

// 测试模块申请响应
message ApplyTestModuleResponse {
  TestModuleApply test_module_apply = 1;
}


// 测试模块申请列表请求
message ListTestModuleAppliesRequest {
  int32 page = 1;
  int32 page_size = 2;
  string search = 3;
}

// 测试模块申请列表响应
message ListTestModuleAppliesResponse {
  repeated TestModuleApply test_module_applies = 1;
  int64 total = 2;
  int32 page = 3;
  int32 page_size = 4;
}

// 测试模块申请处理请求
message ProcessTestModuleApplyRequest {
  int64 id = 1;
  int32 approved = 2;
}

// 测试模块申请处理响应
message ProcessTestModuleApplyResponse {
  bool success = 1;
}

// 测试记录创建请求（开始测试）
message CreateTestRecordRequest {
  string record_number = 1;
  string description = 2;
  string module = 3;
  int64 created_by = 4;
}

// 测试记录创建响应
message CreateTestRecordResponse {
  TestRecord test_record = 1;
}

// 测试记录获取请求
message GetTestRecordRequest {
  string record_number = 1;
}

// 测试记录获取响应
message GetTestRecordResponse {
  TestRecord test_record = 1;
}

// 测试记录删除请求
message DeleteTestRecordRequest {
  string record_number = 1;
}

// 测试记录删除响应
message DeleteTestRecordResponse {
  bool success = 1;
}

// 测试记录列表请求
message ListTestRecordsRequest {
  int32 page = 1;
  int32 page_size = 2;
  string search = 3;
}

// 测试记录列表响应
message ListTestRecordsResponse {
  repeated TestRecord test_records = 1;
  int64 total = 2;
  int32 page = 3;
  int32 page_size = 4;
}

// 测试执行记录创建请求
message CreateTestExecutionRequest {
  int64 test_id = 1;
  TestStatus status = 2;
  string result = 3;
  string error_message = 4;
  int64 duration_ms = 5;
  int64 executed_by = 6;
  string environment = 7;
}

// 测试执行记录创建响应
message CreateTestExecutionResponse {
  TestExecution test_execution = 1;
}

// 测试执行记录获取请求
message GetTestExecutionRequest {
  int64 id = 1;
}

// 测试执行记录获取响应
message GetTestExecutionResponse {
  TestExecution test_execution = 1;
}

// 测试执行记录列表请求
message ListTestExecutionsRequest {
  int32 page = 1;
  int32 page_size = 2;
  string search = 3;
}

// 测试执行记录列表响应
message ListTestExecutionsResponse {
  repeated TestExecution test_executions = 1;
  int64 total = 2;
  int32 page = 3;
  int32 page_size = 4;
}