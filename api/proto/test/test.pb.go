// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.12.4
// source: api/proto/test/test.proto

package test

import (
	timestamp "github.com/golang/protobuf/ptypes/timestamp"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 测试用例状态
type TestStatus int32

const (
	TestStatus_PENDING TestStatus = 0
	TestStatus_RUNNING TestStatus = 1
	TestStatus_PASSED  TestStatus = 2
	TestStatus_FAILED  TestStatus = 3
	TestStatus_SKIPPED TestStatus = 4
)

// Enum value maps for TestStatus.
var (
	TestStatus_name = map[int32]string{
		0: "PENDING",
		1: "RUNNING",
		2: "PASSED",
		3: "FAILED",
		4: "SKIPPED",
	}
	TestStatus_value = map[string]int32{
		"PENDING": 0,
		"RUNNING": 1,
		"PASSED":  2,
		"FAILED":  3,
		"SKIPPED": 4,
	}
)

func (x TestStatus) Enum() *TestStatus {
	p := new(TestStatus)
	*p = x
	return p
}

func (x TestStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TestStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_proto_test_test_proto_enumTypes[0].Descriptor()
}

func (TestStatus) Type() protoreflect.EnumType {
	return &file_api_proto_test_test_proto_enumTypes[0]
}

func (x TestStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TestStatus.Descriptor instead.
func (TestStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{0}
}

type KeyValuePair struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Values        map[string]string      `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *KeyValuePair) Reset() {
	*x = KeyValuePair{}
	mi := &file_api_proto_test_test_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KeyValuePair) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KeyValuePair) ProtoMessage() {}

func (x *KeyValuePair) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KeyValuePair.ProtoReflect.Descriptor instead.
func (*KeyValuePair) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{0}
}

func (x *KeyValuePair) GetValues() map[string]string {
	if x != nil {
		return x.Values
	}
	return nil
}

// 获取服务信息请求
type GetServiceInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceInfoRequest) Reset() {
	*x = GetServiceInfoRequest{}
	mi := &file_api_proto_test_test_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceInfoRequest) ProtoMessage() {}

func (x *GetServiceInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceInfoRequest.ProtoReflect.Descriptor instead.
func (*GetServiceInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{1}
}

// 获取服务信息响应
type GetServiceInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Version       string                 `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Time          string                 `protobuf:"bytes,3,opt,name=time,proto3" json:"time,omitempty"`
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceInfoResponse) Reset() {
	*x = GetServiceInfoResponse{}
	mi := &file_api_proto_test_test_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceInfoResponse) ProtoMessage() {}

func (x *GetServiceInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceInfoResponse.ProtoReflect.Descriptor instead.
func (*GetServiceInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{2}
}

func (x *GetServiceInfoResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetServiceInfoResponse) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *GetServiceInfoResponse) GetTime() string {
	if x != nil {
		return x.Time
	}
	return ""
}

func (x *GetServiceInfoResponse) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// 测试模块
type TestModule struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Id            int64                    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Plugin        string                   `protobuf:"bytes,3,opt,name=plugin,proto3" json:"plugin,omitempty"`
	Version       string                   `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`
	Itmes         map[string]*KeyValuePair `protobuf:"bytes,5,rep,name=itmes,proto3" json:"itmes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Description   string                   `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	CreatedAt     *timestamp.Timestamp     `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamp.Timestamp     `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TestModule) Reset() {
	*x = TestModule{}
	mi := &file_api_proto_test_test_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TestModule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestModule) ProtoMessage() {}

func (x *TestModule) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestModule.ProtoReflect.Descriptor instead.
func (*TestModule) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{3}
}

func (x *TestModule) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TestModule) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TestModule) GetPlugin() string {
	if x != nil {
		return x.Plugin
	}
	return ""
}

func (x *TestModule) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *TestModule) GetItmes() map[string]*KeyValuePair {
	if x != nil {
		return x.Itmes
	}
	return nil
}

func (x *TestModule) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *TestModule) GetCreatedAt() *timestamp.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *TestModule) GetUpdatedAt() *timestamp.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// 测试模块申请
type TestModuleApply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	RequestBy     int64                  `protobuf:"varint,2,opt,name=request_by,json=requestBy,proto3" json:"request_by,omitempty"`
	Modules       []string               `protobuf:"bytes,3,rep,name=modules,proto3" json:"modules,omitempty"`
	Approved      int32                  `protobuf:"varint,4,opt,name=approved,proto3" json:"approved,omitempty"`
	CreatedAt     *timestamp.Timestamp   `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TestModuleApply) Reset() {
	*x = TestModuleApply{}
	mi := &file_api_proto_test_test_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TestModuleApply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestModuleApply) ProtoMessage() {}

func (x *TestModuleApply) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestModuleApply.ProtoReflect.Descriptor instead.
func (*TestModuleApply) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{4}
}

func (x *TestModuleApply) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TestModuleApply) GetRequestBy() int64 {
	if x != nil {
		return x.RequestBy
	}
	return 0
}

func (x *TestModuleApply) GetModules() []string {
	if x != nil {
		return x.Modules
	}
	return nil
}

func (x *TestModuleApply) GetApproved() int32 {
	if x != nil {
		return x.Approved
	}
	return 0
}

func (x *TestModuleApply) GetCreatedAt() *timestamp.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

// 测试记录模型
type TestRecord struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	RecordNumber  string                 `protobuf:"bytes,2,opt,name=RecordNumber,proto3" json:"RecordNumber,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Module        string                 `protobuf:"bytes,4,opt,name=module,proto3" json:"module,omitempty"`
	Result        string                 `protobuf:"bytes,5,opt,name=result,proto3" json:"result,omitempty"`
	CreatedBy     int64                  `protobuf:"varint,6,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	CreatedAt     *timestamp.Timestamp   `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamp.Timestamp   `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TestRecord) Reset() {
	*x = TestRecord{}
	mi := &file_api_proto_test_test_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TestRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestRecord) ProtoMessage() {}

func (x *TestRecord) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestRecord.ProtoReflect.Descriptor instead.
func (*TestRecord) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{5}
}

func (x *TestRecord) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TestRecord) GetRecordNumber() string {
	if x != nil {
		return x.RecordNumber
	}
	return ""
}

func (x *TestRecord) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *TestRecord) GetModule() string {
	if x != nil {
		return x.Module
	}
	return ""
}

func (x *TestRecord) GetResult() string {
	if x != nil {
		return x.Result
	}
	return ""
}

func (x *TestRecord) GetCreatedBy() int64 {
	if x != nil {
		return x.CreatedBy
	}
	return 0
}

func (x *TestRecord) GetCreatedAt() *timestamp.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *TestRecord) GetUpdatedAt() *timestamp.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// 测试执行记录
type TestExecution struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	TestId        int64                  `protobuf:"varint,2,opt,name=test_id,json=testId,proto3" json:"test_id,omitempty"`
	Status        TestStatus             `protobuf:"varint,3,opt,name=status,proto3,enum=test.TestStatus" json:"status,omitempty"`
	Result        string                 `protobuf:"bytes,4,opt,name=result,proto3" json:"result,omitempty"`
	ErrorMessage  string                 `protobuf:"bytes,5,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	DurationMs    int64                  `protobuf:"varint,6,opt,name=duration_ms,json=durationMs,proto3" json:"duration_ms,omitempty"`
	ExecutedBy    int64                  `protobuf:"varint,7,opt,name=executed_by,json=executedBy,proto3" json:"executed_by,omitempty"`
	Environment   string                 `protobuf:"bytes,8,opt,name=environment,proto3" json:"environment,omitempty"`
	StartedAt     *timestamp.Timestamp   `protobuf:"bytes,9,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	FinishedAt    *timestamp.Timestamp   `protobuf:"bytes,10,opt,name=finished_at,json=finishedAt,proto3" json:"finished_at,omitempty"`
	CreatedAt     *timestamp.Timestamp   `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamp.Timestamp   `protobuf:"bytes,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TestExecution) Reset() {
	*x = TestExecution{}
	mi := &file_api_proto_test_test_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TestExecution) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestExecution) ProtoMessage() {}

func (x *TestExecution) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestExecution.ProtoReflect.Descriptor instead.
func (*TestExecution) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{6}
}

func (x *TestExecution) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TestExecution) GetTestId() int64 {
	if x != nil {
		return x.TestId
	}
	return 0
}

func (x *TestExecution) GetStatus() TestStatus {
	if x != nil {
		return x.Status
	}
	return TestStatus_PENDING
}

func (x *TestExecution) GetResult() string {
	if x != nil {
		return x.Result
	}
	return ""
}

func (x *TestExecution) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *TestExecution) GetDurationMs() int64 {
	if x != nil {
		return x.DurationMs
	}
	return 0
}

func (x *TestExecution) GetExecutedBy() int64 {
	if x != nil {
		return x.ExecutedBy
	}
	return 0
}

func (x *TestExecution) GetEnvironment() string {
	if x != nil {
		return x.Environment
	}
	return ""
}

func (x *TestExecution) GetStartedAt() *timestamp.Timestamp {
	if x != nil {
		return x.StartedAt
	}
	return nil
}

func (x *TestExecution) GetFinishedAt() *timestamp.Timestamp {
	if x != nil {
		return x.FinishedAt
	}
	return nil
}

func (x *TestExecution) GetCreatedAt() *timestamp.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *TestExecution) GetUpdatedAt() *timestamp.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// 创建测试模块请求
type CreateTestModuleRequest struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Name          string                   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Plugin        string                   `protobuf:"bytes,2,opt,name=plugin,proto3" json:"plugin,omitempty"`
	Version       string                   `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	Items         map[string]*KeyValuePair `protobuf:"bytes,4,rep,name=items,proto3" json:"items,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Description   string                   `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTestModuleRequest) Reset() {
	*x = CreateTestModuleRequest{}
	mi := &file_api_proto_test_test_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTestModuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTestModuleRequest) ProtoMessage() {}

func (x *CreateTestModuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTestModuleRequest.ProtoReflect.Descriptor instead.
func (*CreateTestModuleRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{7}
}

func (x *CreateTestModuleRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateTestModuleRequest) GetPlugin() string {
	if x != nil {
		return x.Plugin
	}
	return ""
}

func (x *CreateTestModuleRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *CreateTestModuleRequest) GetItems() map[string]*KeyValuePair {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *CreateTestModuleRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// 创建测试模块响应
type CreateTestModuleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TestModule    *TestModule            `protobuf:"bytes,1,opt,name=test_module,json=testModule,proto3" json:"test_module,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTestModuleResponse) Reset() {
	*x = CreateTestModuleResponse{}
	mi := &file_api_proto_test_test_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTestModuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTestModuleResponse) ProtoMessage() {}

func (x *CreateTestModuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTestModuleResponse.ProtoReflect.Descriptor instead.
func (*CreateTestModuleResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{8}
}

func (x *CreateTestModuleResponse) GetTestModule() *TestModule {
	if x != nil {
		return x.TestModule
	}
	return nil
}

// 获取测试模块请求
type GetTestModuleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTestModuleRequest) Reset() {
	*x = GetTestModuleRequest{}
	mi := &file_api_proto_test_test_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTestModuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTestModuleRequest) ProtoMessage() {}

func (x *GetTestModuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTestModuleRequest.ProtoReflect.Descriptor instead.
func (*GetTestModuleRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{9}
}

func (x *GetTestModuleRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// 获取测试模块响应
type GetTestModuleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TestModule    *TestModule            `protobuf:"bytes,1,opt,name=test_module,json=testModule,proto3" json:"test_module,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTestModuleResponse) Reset() {
	*x = GetTestModuleResponse{}
	mi := &file_api_proto_test_test_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTestModuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTestModuleResponse) ProtoMessage() {}

func (x *GetTestModuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTestModuleResponse.ProtoReflect.Descriptor instead.
func (*GetTestModuleResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{10}
}

func (x *GetTestModuleResponse) GetTestModule() *TestModule {
	if x != nil {
		return x.TestModule
	}
	return nil
}

// 更新测试模块请求
type UpdateTestModuleRequest struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Name          string                   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Plugin        string                   `protobuf:"bytes,3,opt,name=plugin,proto3" json:"plugin,omitempty"`
	Version       string                   `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`
	Items         map[string]*KeyValuePair `protobuf:"bytes,5,rep,name=items,proto3" json:"items,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Description   string                   `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateTestModuleRequest) Reset() {
	*x = UpdateTestModuleRequest{}
	mi := &file_api_proto_test_test_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTestModuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTestModuleRequest) ProtoMessage() {}

func (x *UpdateTestModuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTestModuleRequest.ProtoReflect.Descriptor instead.
func (*UpdateTestModuleRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateTestModuleRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateTestModuleRequest) GetPlugin() string {
	if x != nil {
		return x.Plugin
	}
	return ""
}

func (x *UpdateTestModuleRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *UpdateTestModuleRequest) GetItems() map[string]*KeyValuePair {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *UpdateTestModuleRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// 更新测试模块响应
type UpdateTestModuleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TestModule    *TestModule            `protobuf:"bytes,1,opt,name=test_module,json=testModule,proto3" json:"test_module,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateTestModuleResponse) Reset() {
	*x = UpdateTestModuleResponse{}
	mi := &file_api_proto_test_test_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTestModuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTestModuleResponse) ProtoMessage() {}

func (x *UpdateTestModuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTestModuleResponse.ProtoReflect.Descriptor instead.
func (*UpdateTestModuleResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateTestModuleResponse) GetTestModule() *TestModule {
	if x != nil {
		return x.TestModule
	}
	return nil
}

// 删除测试模块请求
type DeleteTestModuleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteTestModuleRequest) Reset() {
	*x = DeleteTestModuleRequest{}
	mi := &file_api_proto_test_test_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteTestModuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTestModuleRequest) ProtoMessage() {}

func (x *DeleteTestModuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTestModuleRequest.ProtoReflect.Descriptor instead.
func (*DeleteTestModuleRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{13}
}

func (x *DeleteTestModuleRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// 删除测试模块响应
type DeleteTestModuleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteTestModuleResponse) Reset() {
	*x = DeleteTestModuleResponse{}
	mi := &file_api_proto_test_test_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteTestModuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTestModuleResponse) ProtoMessage() {}

func (x *DeleteTestModuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTestModuleResponse.ProtoReflect.Descriptor instead.
func (*DeleteTestModuleResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{14}
}

func (x *DeleteTestModuleResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// 测试模块列表请求
type ListTestModulesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Search        string                 `protobuf:"bytes,3,opt,name=search,proto3" json:"search,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTestModulesRequest) Reset() {
	*x = ListTestModulesRequest{}
	mi := &file_api_proto_test_test_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTestModulesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTestModulesRequest) ProtoMessage() {}

func (x *ListTestModulesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTestModulesRequest.ProtoReflect.Descriptor instead.
func (*ListTestModulesRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{15}
}

func (x *ListTestModulesRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListTestModulesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListTestModulesRequest) GetSearch() string {
	if x != nil {
		return x.Search
	}
	return ""
}

// 测试模块列表响应
type ListTestModulesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TestModules   []*TestModule          `protobuf:"bytes,1,rep,name=test_modules,json=testModules,proto3" json:"test_modules,omitempty"`
	Total         int64                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTestModulesResponse) Reset() {
	*x = ListTestModulesResponse{}
	mi := &file_api_proto_test_test_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTestModulesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTestModulesResponse) ProtoMessage() {}

func (x *ListTestModulesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTestModulesResponse.ProtoReflect.Descriptor instead.
func (*ListTestModulesResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{16}
}

func (x *ListTestModulesResponse) GetTestModules() []*TestModule {
	if x != nil {
		return x.TestModules
	}
	return nil
}

func (x *ListTestModulesResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListTestModulesResponse) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListTestModulesResponse) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// 下载测试模块请求
type DownloadTestModuleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DownloadTestModuleRequest) Reset() {
	*x = DownloadTestModuleRequest{}
	mi := &file_api_proto_test_test_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DownloadTestModuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadTestModuleRequest) ProtoMessage() {}

func (x *DownloadTestModuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadTestModuleRequest.ProtoReflect.Descriptor instead.
func (*DownloadTestModuleRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{17}
}

func (x *DownloadTestModuleRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// 下载测试模块响应
type DownloadTestModuleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       []byte                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DownloadTestModuleResponse) Reset() {
	*x = DownloadTestModuleResponse{}
	mi := &file_api_proto_test_test_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DownloadTestModuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadTestModuleResponse) ProtoMessage() {}

func (x *DownloadTestModuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadTestModuleResponse.ProtoReflect.Descriptor instead.
func (*DownloadTestModuleResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{18}
}

func (x *DownloadTestModuleResponse) GetContent() []byte {
	if x != nil {
		return x.Content
	}
	return nil
}

// 上传测试模块请求
type UploadTestModuleRequest struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Name          string                   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Plugin        string                   `protobuf:"bytes,2,opt,name=plugin,proto3" json:"plugin,omitempty"`
	Version       string                   `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	Items         map[string]*KeyValuePair `protobuf:"bytes,4,rep,name=items,proto3" json:"items,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Description   string                   `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	Content       []byte                   `protobuf:"bytes,6,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UploadTestModuleRequest) Reset() {
	*x = UploadTestModuleRequest{}
	mi := &file_api_proto_test_test_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadTestModuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadTestModuleRequest) ProtoMessage() {}

func (x *UploadTestModuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadTestModuleRequest.ProtoReflect.Descriptor instead.
func (*UploadTestModuleRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{19}
}

func (x *UploadTestModuleRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UploadTestModuleRequest) GetPlugin() string {
	if x != nil {
		return x.Plugin
	}
	return ""
}

func (x *UploadTestModuleRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *UploadTestModuleRequest) GetItems() map[string]*KeyValuePair {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *UploadTestModuleRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UploadTestModuleRequest) GetContent() []byte {
	if x != nil {
		return x.Content
	}
	return nil
}

// 上传测试模块响应
type UploadTestModuleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TestModule    *TestModule            `protobuf:"bytes,1,opt,name=test_module,json=testModule,proto3" json:"test_module,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UploadTestModuleResponse) Reset() {
	*x = UploadTestModuleResponse{}
	mi := &file_api_proto_test_test_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadTestModuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadTestModuleResponse) ProtoMessage() {}

func (x *UploadTestModuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadTestModuleResponse.ProtoReflect.Descriptor instead.
func (*UploadTestModuleResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{20}
}

func (x *UploadTestModuleResponse) GetTestModule() *TestModule {
	if x != nil {
		return x.TestModule
	}
	return nil
}

// 测试模块申请请求
type ApplyTestModuleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	RequestBy     int64                  `protobuf:"varint,2,opt,name=request_by,json=requestBy,proto3" json:"request_by,omitempty"`
	Modules       []string               `protobuf:"bytes,3,rep,name=modules,proto3" json:"modules,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ApplyTestModuleRequest) Reset() {
	*x = ApplyTestModuleRequest{}
	mi := &file_api_proto_test_test_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApplyTestModuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyTestModuleRequest) ProtoMessage() {}

func (x *ApplyTestModuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyTestModuleRequest.ProtoReflect.Descriptor instead.
func (*ApplyTestModuleRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{21}
}

func (x *ApplyTestModuleRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ApplyTestModuleRequest) GetRequestBy() int64 {
	if x != nil {
		return x.RequestBy
	}
	return 0
}

func (x *ApplyTestModuleRequest) GetModules() []string {
	if x != nil {
		return x.Modules
	}
	return nil
}

// 测试模块申请响应
type ApplyTestModuleResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	TestModuleApply *TestModuleApply       `protobuf:"bytes,1,opt,name=test_module_apply,json=testModuleApply,proto3" json:"test_module_apply,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ApplyTestModuleResponse) Reset() {
	*x = ApplyTestModuleResponse{}
	mi := &file_api_proto_test_test_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApplyTestModuleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyTestModuleResponse) ProtoMessage() {}

func (x *ApplyTestModuleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyTestModuleResponse.ProtoReflect.Descriptor instead.
func (*ApplyTestModuleResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{22}
}

func (x *ApplyTestModuleResponse) GetTestModuleApply() *TestModuleApply {
	if x != nil {
		return x.TestModuleApply
	}
	return nil
}

// 测试模块申请列表请求
type ListTestModuleAppliesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Search        string                 `protobuf:"bytes,3,opt,name=search,proto3" json:"search,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTestModuleAppliesRequest) Reset() {
	*x = ListTestModuleAppliesRequest{}
	mi := &file_api_proto_test_test_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTestModuleAppliesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTestModuleAppliesRequest) ProtoMessage() {}

func (x *ListTestModuleAppliesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTestModuleAppliesRequest.ProtoReflect.Descriptor instead.
func (*ListTestModuleAppliesRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{23}
}

func (x *ListTestModuleAppliesRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListTestModuleAppliesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListTestModuleAppliesRequest) GetSearch() string {
	if x != nil {
		return x.Search
	}
	return ""
}

// 测试模块申请列表响应
type ListTestModuleAppliesResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	TestModuleApplies []*TestModuleApply     `protobuf:"bytes,1,rep,name=test_module_applies,json=testModuleApplies,proto3" json:"test_module_applies,omitempty"`
	Total             int64                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	Page              int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize          int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ListTestModuleAppliesResponse) Reset() {
	*x = ListTestModuleAppliesResponse{}
	mi := &file_api_proto_test_test_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTestModuleAppliesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTestModuleAppliesResponse) ProtoMessage() {}

func (x *ListTestModuleAppliesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTestModuleAppliesResponse.ProtoReflect.Descriptor instead.
func (*ListTestModuleAppliesResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{24}
}

func (x *ListTestModuleAppliesResponse) GetTestModuleApplies() []*TestModuleApply {
	if x != nil {
		return x.TestModuleApplies
	}
	return nil
}

func (x *ListTestModuleAppliesResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListTestModuleAppliesResponse) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListTestModuleAppliesResponse) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// 测试模块申请处理请求
type ProcessTestModuleApplyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Approved      int32                  `protobuf:"varint,2,opt,name=approved,proto3" json:"approved,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProcessTestModuleApplyRequest) Reset() {
	*x = ProcessTestModuleApplyRequest{}
	mi := &file_api_proto_test_test_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcessTestModuleApplyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessTestModuleApplyRequest) ProtoMessage() {}

func (x *ProcessTestModuleApplyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessTestModuleApplyRequest.ProtoReflect.Descriptor instead.
func (*ProcessTestModuleApplyRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{25}
}

func (x *ProcessTestModuleApplyRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ProcessTestModuleApplyRequest) GetApproved() int32 {
	if x != nil {
		return x.Approved
	}
	return 0
}

// 测试模块申请处理响应
type ProcessTestModuleApplyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProcessTestModuleApplyResponse) Reset() {
	*x = ProcessTestModuleApplyResponse{}
	mi := &file_api_proto_test_test_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcessTestModuleApplyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessTestModuleApplyResponse) ProtoMessage() {}

func (x *ProcessTestModuleApplyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessTestModuleApplyResponse.ProtoReflect.Descriptor instead.
func (*ProcessTestModuleApplyResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{26}
}

func (x *ProcessTestModuleApplyResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// 测试记录创建请求（开始测试）
type CreateTestRecordRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RecordNumber  string                 `protobuf:"bytes,1,opt,name=record_number,json=recordNumber,proto3" json:"record_number,omitempty"`
	Description   string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	Module        string                 `protobuf:"bytes,3,opt,name=module,proto3" json:"module,omitempty"`
	CreatedBy     int64                  `protobuf:"varint,4,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTestRecordRequest) Reset() {
	*x = CreateTestRecordRequest{}
	mi := &file_api_proto_test_test_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTestRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTestRecordRequest) ProtoMessage() {}

func (x *CreateTestRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTestRecordRequest.ProtoReflect.Descriptor instead.
func (*CreateTestRecordRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{27}
}

func (x *CreateTestRecordRequest) GetRecordNumber() string {
	if x != nil {
		return x.RecordNumber
	}
	return ""
}

func (x *CreateTestRecordRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateTestRecordRequest) GetModule() string {
	if x != nil {
		return x.Module
	}
	return ""
}

func (x *CreateTestRecordRequest) GetCreatedBy() int64 {
	if x != nil {
		return x.CreatedBy
	}
	return 0
}

// 测试记录创建响应
type CreateTestRecordResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TestRecord    *TestRecord            `protobuf:"bytes,1,opt,name=test_record,json=testRecord,proto3" json:"test_record,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTestRecordResponse) Reset() {
	*x = CreateTestRecordResponse{}
	mi := &file_api_proto_test_test_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTestRecordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTestRecordResponse) ProtoMessage() {}

func (x *CreateTestRecordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTestRecordResponse.ProtoReflect.Descriptor instead.
func (*CreateTestRecordResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{28}
}

func (x *CreateTestRecordResponse) GetTestRecord() *TestRecord {
	if x != nil {
		return x.TestRecord
	}
	return nil
}

// 测试记录获取请求
type GetTestRecordRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RecordNumber  string                 `protobuf:"bytes,1,opt,name=record_number,json=recordNumber,proto3" json:"record_number,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTestRecordRequest) Reset() {
	*x = GetTestRecordRequest{}
	mi := &file_api_proto_test_test_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTestRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTestRecordRequest) ProtoMessage() {}

func (x *GetTestRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTestRecordRequest.ProtoReflect.Descriptor instead.
func (*GetTestRecordRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{29}
}

func (x *GetTestRecordRequest) GetRecordNumber() string {
	if x != nil {
		return x.RecordNumber
	}
	return ""
}

// 测试记录获取响应
type GetTestRecordResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TestRecord    *TestRecord            `protobuf:"bytes,1,opt,name=test_record,json=testRecord,proto3" json:"test_record,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTestRecordResponse) Reset() {
	*x = GetTestRecordResponse{}
	mi := &file_api_proto_test_test_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTestRecordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTestRecordResponse) ProtoMessage() {}

func (x *GetTestRecordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTestRecordResponse.ProtoReflect.Descriptor instead.
func (*GetTestRecordResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{30}
}

func (x *GetTestRecordResponse) GetTestRecord() *TestRecord {
	if x != nil {
		return x.TestRecord
	}
	return nil
}

// 测试记录删除请求
type DeleteTestRecordRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RecordNumber  string                 `protobuf:"bytes,1,opt,name=record_number,json=recordNumber,proto3" json:"record_number,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteTestRecordRequest) Reset() {
	*x = DeleteTestRecordRequest{}
	mi := &file_api_proto_test_test_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteTestRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTestRecordRequest) ProtoMessage() {}

func (x *DeleteTestRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTestRecordRequest.ProtoReflect.Descriptor instead.
func (*DeleteTestRecordRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{31}
}

func (x *DeleteTestRecordRequest) GetRecordNumber() string {
	if x != nil {
		return x.RecordNumber
	}
	return ""
}

// 测试记录删除响应
type DeleteTestRecordResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteTestRecordResponse) Reset() {
	*x = DeleteTestRecordResponse{}
	mi := &file_api_proto_test_test_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteTestRecordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTestRecordResponse) ProtoMessage() {}

func (x *DeleteTestRecordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTestRecordResponse.ProtoReflect.Descriptor instead.
func (*DeleteTestRecordResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{32}
}

func (x *DeleteTestRecordResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// 测试记录列表请求
type ListTestRecordsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Search        string                 `protobuf:"bytes,3,opt,name=search,proto3" json:"search,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTestRecordsRequest) Reset() {
	*x = ListTestRecordsRequest{}
	mi := &file_api_proto_test_test_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTestRecordsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTestRecordsRequest) ProtoMessage() {}

func (x *ListTestRecordsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTestRecordsRequest.ProtoReflect.Descriptor instead.
func (*ListTestRecordsRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{33}
}

func (x *ListTestRecordsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListTestRecordsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListTestRecordsRequest) GetSearch() string {
	if x != nil {
		return x.Search
	}
	return ""
}

// 测试记录列表响应
type ListTestRecordsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TestRecords   []*TestRecord          `protobuf:"bytes,1,rep,name=test_records,json=testRecords,proto3" json:"test_records,omitempty"`
	Total         int64                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTestRecordsResponse) Reset() {
	*x = ListTestRecordsResponse{}
	mi := &file_api_proto_test_test_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTestRecordsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTestRecordsResponse) ProtoMessage() {}

func (x *ListTestRecordsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTestRecordsResponse.ProtoReflect.Descriptor instead.
func (*ListTestRecordsResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{34}
}

func (x *ListTestRecordsResponse) GetTestRecords() []*TestRecord {
	if x != nil {
		return x.TestRecords
	}
	return nil
}

func (x *ListTestRecordsResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListTestRecordsResponse) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListTestRecordsResponse) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// 测试执行记录创建请求
type CreateTestExecutionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TestId        int64                  `protobuf:"varint,1,opt,name=test_id,json=testId,proto3" json:"test_id,omitempty"`
	Status        TestStatus             `protobuf:"varint,2,opt,name=status,proto3,enum=test.TestStatus" json:"status,omitempty"`
	Result        string                 `protobuf:"bytes,3,opt,name=result,proto3" json:"result,omitempty"`
	ErrorMessage  string                 `protobuf:"bytes,4,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	DurationMs    int64                  `protobuf:"varint,5,opt,name=duration_ms,json=durationMs,proto3" json:"duration_ms,omitempty"`
	ExecutedBy    int64                  `protobuf:"varint,6,opt,name=executed_by,json=executedBy,proto3" json:"executed_by,omitempty"`
	Environment   string                 `protobuf:"bytes,7,opt,name=environment,proto3" json:"environment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTestExecutionRequest) Reset() {
	*x = CreateTestExecutionRequest{}
	mi := &file_api_proto_test_test_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTestExecutionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTestExecutionRequest) ProtoMessage() {}

func (x *CreateTestExecutionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTestExecutionRequest.ProtoReflect.Descriptor instead.
func (*CreateTestExecutionRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{35}
}

func (x *CreateTestExecutionRequest) GetTestId() int64 {
	if x != nil {
		return x.TestId
	}
	return 0
}

func (x *CreateTestExecutionRequest) GetStatus() TestStatus {
	if x != nil {
		return x.Status
	}
	return TestStatus_PENDING
}

func (x *CreateTestExecutionRequest) GetResult() string {
	if x != nil {
		return x.Result
	}
	return ""
}

func (x *CreateTestExecutionRequest) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *CreateTestExecutionRequest) GetDurationMs() int64 {
	if x != nil {
		return x.DurationMs
	}
	return 0
}

func (x *CreateTestExecutionRequest) GetExecutedBy() int64 {
	if x != nil {
		return x.ExecutedBy
	}
	return 0
}

func (x *CreateTestExecutionRequest) GetEnvironment() string {
	if x != nil {
		return x.Environment
	}
	return ""
}

// 测试执行记录创建响应
type CreateTestExecutionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TestExecution *TestExecution         `protobuf:"bytes,1,opt,name=test_execution,json=testExecution,proto3" json:"test_execution,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTestExecutionResponse) Reset() {
	*x = CreateTestExecutionResponse{}
	mi := &file_api_proto_test_test_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTestExecutionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTestExecutionResponse) ProtoMessage() {}

func (x *CreateTestExecutionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTestExecutionResponse.ProtoReflect.Descriptor instead.
func (*CreateTestExecutionResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{36}
}

func (x *CreateTestExecutionResponse) GetTestExecution() *TestExecution {
	if x != nil {
		return x.TestExecution
	}
	return nil
}

// 测试执行记录获取请求
type GetTestExecutionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTestExecutionRequest) Reset() {
	*x = GetTestExecutionRequest{}
	mi := &file_api_proto_test_test_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTestExecutionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTestExecutionRequest) ProtoMessage() {}

func (x *GetTestExecutionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTestExecutionRequest.ProtoReflect.Descriptor instead.
func (*GetTestExecutionRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{37}
}

func (x *GetTestExecutionRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 测试执行记录获取响应
type GetTestExecutionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TestExecution *TestExecution         `protobuf:"bytes,1,opt,name=test_execution,json=testExecution,proto3" json:"test_execution,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTestExecutionResponse) Reset() {
	*x = GetTestExecutionResponse{}
	mi := &file_api_proto_test_test_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTestExecutionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTestExecutionResponse) ProtoMessage() {}

func (x *GetTestExecutionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTestExecutionResponse.ProtoReflect.Descriptor instead.
func (*GetTestExecutionResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{38}
}

func (x *GetTestExecutionResponse) GetTestExecution() *TestExecution {
	if x != nil {
		return x.TestExecution
	}
	return nil
}

// 测试执行记录列表请求
type ListTestExecutionsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Search        string                 `protobuf:"bytes,3,opt,name=search,proto3" json:"search,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTestExecutionsRequest) Reset() {
	*x = ListTestExecutionsRequest{}
	mi := &file_api_proto_test_test_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTestExecutionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTestExecutionsRequest) ProtoMessage() {}

func (x *ListTestExecutionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTestExecutionsRequest.ProtoReflect.Descriptor instead.
func (*ListTestExecutionsRequest) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{39}
}

func (x *ListTestExecutionsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListTestExecutionsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListTestExecutionsRequest) GetSearch() string {
	if x != nil {
		return x.Search
	}
	return ""
}

// 测试执行记录列表响应
type ListTestExecutionsResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	TestExecutions []*TestExecution       `protobuf:"bytes,1,rep,name=test_executions,json=testExecutions,proto3" json:"test_executions,omitempty"`
	Total          int64                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	Page           int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize       int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ListTestExecutionsResponse) Reset() {
	*x = ListTestExecutionsResponse{}
	mi := &file_api_proto_test_test_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTestExecutionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTestExecutionsResponse) ProtoMessage() {}

func (x *ListTestExecutionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_proto_test_test_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTestExecutionsResponse.ProtoReflect.Descriptor instead.
func (*ListTestExecutionsResponse) Descriptor() ([]byte, []int) {
	return file_api_proto_test_test_proto_rawDescGZIP(), []int{40}
}

func (x *ListTestExecutionsResponse) GetTestExecutions() []*TestExecution {
	if x != nil {
		return x.TestExecutions
	}
	return nil
}

func (x *ListTestExecutionsResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListTestExecutionsResponse) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListTestExecutionsResponse) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

var File_api_proto_test_test_proto protoreflect.FileDescriptor

const file_api_proto_test_test_proto_rawDesc = "" +
	"\n" +
	"\x19api/proto/test/test.proto\x12\x04test\x1a\x1fgoogle/protobuf/timestamp.proto\"\x81\x01\n" +
	"\fKeyValuePair\x126\n" +
	"\x06values\x18\x01 \x03(\v2\x1e.test.KeyValuePair.ValuesEntryR\x06values\x1a9\n" +
	"\vValuesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x17\n" +
	"\x15GetServiceInfoRequest\"|\n" +
	"\x16GetServiceInfoResponse\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x18\n" +
	"\aversion\x18\x02 \x01(\tR\aversion\x12\x12\n" +
	"\x04time\x18\x03 \x01(\tR\x04time\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\"\xfb\x02\n" +
	"\n" +
	"TestModule\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06plugin\x18\x03 \x01(\tR\x06plugin\x12\x18\n" +
	"\aversion\x18\x04 \x01(\tR\aversion\x121\n" +
	"\x05itmes\x18\x05 \x03(\v2\x1b.test.TestModule.ItmesEntryR\x05itmes\x12 \n" +
	"\vdescription\x18\x06 \x01(\tR\vdescription\x129\n" +
	"\n" +
	"created_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x1aL\n" +
	"\n" +
	"ItmesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12(\n" +
	"\x05value\x18\x02 \x01(\v2\x12.test.KeyValuePairR\x05value:\x028\x01\"\xb1\x01\n" +
	"\x0fTestModuleApply\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1d\n" +
	"\n" +
	"request_by\x18\x02 \x01(\x03R\trequestBy\x12\x18\n" +
	"\amodules\x18\x03 \x03(\tR\amodules\x12\x1a\n" +
	"\bapproved\x18\x04 \x01(\x05R\bapproved\x129\n" +
	"\n" +
	"created_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\"\xa7\x02\n" +
	"\n" +
	"TestRecord\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\"\n" +
	"\fRecordNumber\x18\x02 \x01(\tR\fRecordNumber\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x16\n" +
	"\x06module\x18\x04 \x01(\tR\x06module\x12\x16\n" +
	"\x06result\x18\x05 \x01(\tR\x06result\x12\x1d\n" +
	"\n" +
	"created_by\x18\x06 \x01(\x03R\tcreatedBy\x129\n" +
	"\n" +
	"created_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\xf1\x03\n" +
	"\rTestExecution\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n" +
	"\atest_id\x18\x02 \x01(\x03R\x06testId\x12(\n" +
	"\x06status\x18\x03 \x01(\x0e2\x10.test.TestStatusR\x06status\x12\x16\n" +
	"\x06result\x18\x04 \x01(\tR\x06result\x12#\n" +
	"\rerror_message\x18\x05 \x01(\tR\ferrorMessage\x12\x1f\n" +
	"\vduration_ms\x18\x06 \x01(\x03R\n" +
	"durationMs\x12\x1f\n" +
	"\vexecuted_by\x18\a \x01(\x03R\n" +
	"executedBy\x12 \n" +
	"\venvironment\x18\b \x01(\tR\venvironment\x129\n" +
	"\n" +
	"started_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tstartedAt\x12;\n" +
	"\vfinished_at\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"finishedAt\x129\n" +
	"\n" +
	"created_at\x18\v \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\x8f\x02\n" +
	"\x17CreateTestModuleRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x16\n" +
	"\x06plugin\x18\x02 \x01(\tR\x06plugin\x12\x18\n" +
	"\aversion\x18\x03 \x01(\tR\aversion\x12>\n" +
	"\x05items\x18\x04 \x03(\v2(.test.CreateTestModuleRequest.ItemsEntryR\x05items\x12 \n" +
	"\vdescription\x18\x05 \x01(\tR\vdescription\x1aL\n" +
	"\n" +
	"ItemsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12(\n" +
	"\x05value\x18\x02 \x01(\v2\x12.test.KeyValuePairR\x05value:\x028\x01\"M\n" +
	"\x18CreateTestModuleResponse\x121\n" +
	"\vtest_module\x18\x01 \x01(\v2\x10.test.TestModuleR\n" +
	"testModule\"*\n" +
	"\x14GetTestModuleRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"J\n" +
	"\x15GetTestModuleResponse\x121\n" +
	"\vtest_module\x18\x01 \x01(\v2\x10.test.TestModuleR\n" +
	"testModule\"\x8f\x02\n" +
	"\x17UpdateTestModuleRequest\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06plugin\x18\x03 \x01(\tR\x06plugin\x12\x18\n" +
	"\aversion\x18\x04 \x01(\tR\aversion\x12>\n" +
	"\x05items\x18\x05 \x03(\v2(.test.UpdateTestModuleRequest.ItemsEntryR\x05items\x12 \n" +
	"\vdescription\x18\x06 \x01(\tR\vdescription\x1aL\n" +
	"\n" +
	"ItemsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12(\n" +
	"\x05value\x18\x02 \x01(\v2\x12.test.KeyValuePairR\x05value:\x028\x01\"M\n" +
	"\x18UpdateTestModuleResponse\x121\n" +
	"\vtest_module\x18\x01 \x01(\v2\x10.test.TestModuleR\n" +
	"testModule\"-\n" +
	"\x17DeleteTestModuleRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"4\n" +
	"\x18DeleteTestModuleResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\"a\n" +
	"\x16ListTestModulesRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x16\n" +
	"\x06search\x18\x03 \x01(\tR\x06search\"\x95\x01\n" +
	"\x17ListTestModulesResponse\x123\n" +
	"\ftest_modules\x18\x01 \x03(\v2\x10.test.TestModuleR\vtestModules\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x03R\x05total\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\"/\n" +
	"\x19DownloadTestModuleRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"6\n" +
	"\x1aDownloadTestModuleResponse\x12\x18\n" +
	"\acontent\x18\x01 \x01(\fR\acontent\"\xa9\x02\n" +
	"\x17UploadTestModuleRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x16\n" +
	"\x06plugin\x18\x02 \x01(\tR\x06plugin\x12\x18\n" +
	"\aversion\x18\x03 \x01(\tR\aversion\x12>\n" +
	"\x05items\x18\x04 \x03(\v2(.test.UploadTestModuleRequest.ItemsEntryR\x05items\x12 \n" +
	"\vdescription\x18\x05 \x01(\tR\vdescription\x12\x18\n" +
	"\acontent\x18\x06 \x01(\fR\acontent\x1aL\n" +
	"\n" +
	"ItemsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12(\n" +
	"\x05value\x18\x02 \x01(\v2\x12.test.KeyValuePairR\x05value:\x028\x01\"M\n" +
	"\x18UploadTestModuleResponse\x121\n" +
	"\vtest_module\x18\x01 \x01(\v2\x10.test.TestModuleR\n" +
	"testModule\"a\n" +
	"\x16ApplyTestModuleRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1d\n" +
	"\n" +
	"request_by\x18\x02 \x01(\x03R\trequestBy\x12\x18\n" +
	"\amodules\x18\x03 \x03(\tR\amodules\"\\\n" +
	"\x17ApplyTestModuleResponse\x12A\n" +
	"\x11test_module_apply\x18\x01 \x01(\v2\x15.test.TestModuleApplyR\x0ftestModuleApply\"g\n" +
	"\x1cListTestModuleAppliesRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x16\n" +
	"\x06search\x18\x03 \x01(\tR\x06search\"\xad\x01\n" +
	"\x1dListTestModuleAppliesResponse\x12E\n" +
	"\x13test_module_applies\x18\x01 \x03(\v2\x15.test.TestModuleApplyR\x11testModuleApplies\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x03R\x05total\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\"K\n" +
	"\x1dProcessTestModuleApplyRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1a\n" +
	"\bapproved\x18\x02 \x01(\x05R\bapproved\":\n" +
	"\x1eProcessTestModuleApplyResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\"\x97\x01\n" +
	"\x17CreateTestRecordRequest\x12#\n" +
	"\rrecord_number\x18\x01 \x01(\tR\frecordNumber\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12\x16\n" +
	"\x06module\x18\x03 \x01(\tR\x06module\x12\x1d\n" +
	"\n" +
	"created_by\x18\x04 \x01(\x03R\tcreatedBy\"M\n" +
	"\x18CreateTestRecordResponse\x121\n" +
	"\vtest_record\x18\x01 \x01(\v2\x10.test.TestRecordR\n" +
	"testRecord\";\n" +
	"\x14GetTestRecordRequest\x12#\n" +
	"\rrecord_number\x18\x01 \x01(\tR\frecordNumber\"J\n" +
	"\x15GetTestRecordResponse\x121\n" +
	"\vtest_record\x18\x01 \x01(\v2\x10.test.TestRecordR\n" +
	"testRecord\">\n" +
	"\x17DeleteTestRecordRequest\x12#\n" +
	"\rrecord_number\x18\x01 \x01(\tR\frecordNumber\"4\n" +
	"\x18DeleteTestRecordResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\"a\n" +
	"\x16ListTestRecordsRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x16\n" +
	"\x06search\x18\x03 \x01(\tR\x06search\"\x95\x01\n" +
	"\x17ListTestRecordsResponse\x123\n" +
	"\ftest_records\x18\x01 \x03(\v2\x10.test.TestRecordR\vtestRecords\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x03R\x05total\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\"\x80\x02\n" +
	"\x1aCreateTestExecutionRequest\x12\x17\n" +
	"\atest_id\x18\x01 \x01(\x03R\x06testId\x12(\n" +
	"\x06status\x18\x02 \x01(\x0e2\x10.test.TestStatusR\x06status\x12\x16\n" +
	"\x06result\x18\x03 \x01(\tR\x06result\x12#\n" +
	"\rerror_message\x18\x04 \x01(\tR\ferrorMessage\x12\x1f\n" +
	"\vduration_ms\x18\x05 \x01(\x03R\n" +
	"durationMs\x12\x1f\n" +
	"\vexecuted_by\x18\x06 \x01(\x03R\n" +
	"executedBy\x12 \n" +
	"\venvironment\x18\a \x01(\tR\venvironment\"Y\n" +
	"\x1bCreateTestExecutionResponse\x12:\n" +
	"\x0etest_execution\x18\x01 \x01(\v2\x13.test.TestExecutionR\rtestExecution\")\n" +
	"\x17GetTestExecutionRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"V\n" +
	"\x18GetTestExecutionResponse\x12:\n" +
	"\x0etest_execution\x18\x01 \x01(\v2\x13.test.TestExecutionR\rtestExecution\"d\n" +
	"\x19ListTestExecutionsRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x16\n" +
	"\x06search\x18\x03 \x01(\tR\x06search\"\xa1\x01\n" +
	"\x1aListTestExecutionsResponse\x12<\n" +
	"\x0ftest_executions\x18\x01 \x03(\v2\x13.test.TestExecutionR\x0etestExecutions\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x03R\x05total\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize*K\n" +
	"\n" +
	"TestStatus\x12\v\n" +
	"\aPENDING\x10\x00\x12\v\n" +
	"\aRUNNING\x10\x01\x12\n" +
	"\n" +
	"\x06PASSED\x10\x02\x12\n" +
	"\n" +
	"\x06FAILED\x10\x03\x12\v\n" +
	"\aSKIPPED\x10\x042\xf8\v\n" +
	"\vTestService\x12K\n" +
	"\x0eGetServiceInfo\x12\x1b.test.GetServiceInfoRequest\x1a\x1c.test.GetServiceInfoResponse\x12Q\n" +
	"\x10CreateTestModule\x12\x1d.test.CreateTestModuleRequest\x1a\x1e.test.CreateTestModuleResponse\x12H\n" +
	"\rGetTestModule\x12\x1a.test.GetTestModuleRequest\x1a\x1b.test.GetTestModuleResponse\x12Q\n" +
	"\x10UpdateTestModule\x12\x1d.test.UpdateTestModuleRequest\x1a\x1e.test.UpdateTestModuleResponse\x12Q\n" +
	"\x10DeleteTestModule\x12\x1d.test.DeleteTestModuleRequest\x1a\x1e.test.DeleteTestModuleResponse\x12N\n" +
	"\x0fListTestModules\x12\x1c.test.ListTestModulesRequest\x1a\x1d.test.ListTestModulesResponse\x12W\n" +
	"\x12DownloadTestModule\x12\x1f.test.DownloadTestModuleRequest\x1a .test.DownloadTestModuleResponse\x12Q\n" +
	"\x10UploadTestModule\x12\x1d.test.UploadTestModuleRequest\x1a\x1e.test.UploadTestModuleResponse\x12N\n" +
	"\x0fApplyTestModule\x12\x1c.test.ApplyTestModuleRequest\x1a\x1d.test.ApplyTestModuleResponse\x12`\n" +
	"\x15ListTestModuleApplies\x12\".test.ListTestModuleAppliesRequest\x1a#.test.ListTestModuleAppliesResponse\x12c\n" +
	"\x16ProcessTestModuleApply\x12#.test.ProcessTestModuleApplyRequest\x1a$.test.ProcessTestModuleApplyResponse\x12Q\n" +
	"\x10CreateTestRecord\x12\x1d.test.CreateTestRecordRequest\x1a\x1e.test.CreateTestRecordResponse\x12H\n" +
	"\rGetTestRecord\x12\x1a.test.GetTestRecordRequest\x1a\x1b.test.GetTestRecordResponse\x12Q\n" +
	"\x10DeleteTestRecord\x12\x1d.test.DeleteTestRecordRequest\x1a\x1e.test.DeleteTestRecordResponse\x12N\n" +
	"\x0fListTestRecords\x12\x1c.test.ListTestRecordsRequest\x1a\x1d.test.ListTestRecordsResponse\x12Z\n" +
	"\x13CreateTestExecution\x12 .test.CreateTestExecutionRequest\x1a!.test.CreateTestExecutionResponse\x12Q\n" +
	"\x10GetTestExecution\x12\x1d.test.GetTestExecutionRequest\x1a\x1e.test.GetTestExecutionResponse\x12W\n" +
	"\x12ListTestExecutions\x12\x1f.test.ListTestExecutionsRequest\x1a .test.ListTestExecutionsResponseB(Z&github.com/testplatform/api/proto/testb\x06proto3"

var (
	file_api_proto_test_test_proto_rawDescOnce sync.Once
	file_api_proto_test_test_proto_rawDescData []byte
)

func file_api_proto_test_test_proto_rawDescGZIP() []byte {
	file_api_proto_test_test_proto_rawDescOnce.Do(func() {
		file_api_proto_test_test_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_proto_test_test_proto_rawDesc), len(file_api_proto_test_test_proto_rawDesc)))
	})
	return file_api_proto_test_test_proto_rawDescData
}

var file_api_proto_test_test_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_proto_test_test_proto_msgTypes = make([]protoimpl.MessageInfo, 46)
var file_api_proto_test_test_proto_goTypes = []any{
	(TestStatus)(0),                        // 0: test.TestStatus
	(*KeyValuePair)(nil),                   // 1: test.KeyValuePair
	(*GetServiceInfoRequest)(nil),          // 2: test.GetServiceInfoRequest
	(*GetServiceInfoResponse)(nil),         // 3: test.GetServiceInfoResponse
	(*TestModule)(nil),                     // 4: test.TestModule
	(*TestModuleApply)(nil),                // 5: test.TestModuleApply
	(*TestRecord)(nil),                     // 6: test.TestRecord
	(*TestExecution)(nil),                  // 7: test.TestExecution
	(*CreateTestModuleRequest)(nil),        // 8: test.CreateTestModuleRequest
	(*CreateTestModuleResponse)(nil),       // 9: test.CreateTestModuleResponse
	(*GetTestModuleRequest)(nil),           // 10: test.GetTestModuleRequest
	(*GetTestModuleResponse)(nil),          // 11: test.GetTestModuleResponse
	(*UpdateTestModuleRequest)(nil),        // 12: test.UpdateTestModuleRequest
	(*UpdateTestModuleResponse)(nil),       // 13: test.UpdateTestModuleResponse
	(*DeleteTestModuleRequest)(nil),        // 14: test.DeleteTestModuleRequest
	(*DeleteTestModuleResponse)(nil),       // 15: test.DeleteTestModuleResponse
	(*ListTestModulesRequest)(nil),         // 16: test.ListTestModulesRequest
	(*ListTestModulesResponse)(nil),        // 17: test.ListTestModulesResponse
	(*DownloadTestModuleRequest)(nil),      // 18: test.DownloadTestModuleRequest
	(*DownloadTestModuleResponse)(nil),     // 19: test.DownloadTestModuleResponse
	(*UploadTestModuleRequest)(nil),        // 20: test.UploadTestModuleRequest
	(*UploadTestModuleResponse)(nil),       // 21: test.UploadTestModuleResponse
	(*ApplyTestModuleRequest)(nil),         // 22: test.ApplyTestModuleRequest
	(*ApplyTestModuleResponse)(nil),        // 23: test.ApplyTestModuleResponse
	(*ListTestModuleAppliesRequest)(nil),   // 24: test.ListTestModuleAppliesRequest
	(*ListTestModuleAppliesResponse)(nil),  // 25: test.ListTestModuleAppliesResponse
	(*ProcessTestModuleApplyRequest)(nil),  // 26: test.ProcessTestModuleApplyRequest
	(*ProcessTestModuleApplyResponse)(nil), // 27: test.ProcessTestModuleApplyResponse
	(*CreateTestRecordRequest)(nil),        // 28: test.CreateTestRecordRequest
	(*CreateTestRecordResponse)(nil),       // 29: test.CreateTestRecordResponse
	(*GetTestRecordRequest)(nil),           // 30: test.GetTestRecordRequest
	(*GetTestRecordResponse)(nil),          // 31: test.GetTestRecordResponse
	(*DeleteTestRecordRequest)(nil),        // 32: test.DeleteTestRecordRequest
	(*DeleteTestRecordResponse)(nil),       // 33: test.DeleteTestRecordResponse
	(*ListTestRecordsRequest)(nil),         // 34: test.ListTestRecordsRequest
	(*ListTestRecordsResponse)(nil),        // 35: test.ListTestRecordsResponse
	(*CreateTestExecutionRequest)(nil),     // 36: test.CreateTestExecutionRequest
	(*CreateTestExecutionResponse)(nil),    // 37: test.CreateTestExecutionResponse
	(*GetTestExecutionRequest)(nil),        // 38: test.GetTestExecutionRequest
	(*GetTestExecutionResponse)(nil),       // 39: test.GetTestExecutionResponse
	(*ListTestExecutionsRequest)(nil),      // 40: test.ListTestExecutionsRequest
	(*ListTestExecutionsResponse)(nil),     // 41: test.ListTestExecutionsResponse
	nil,                                    // 42: test.KeyValuePair.ValuesEntry
	nil,                                    // 43: test.TestModule.ItmesEntry
	nil,                                    // 44: test.CreateTestModuleRequest.ItemsEntry
	nil,                                    // 45: test.UpdateTestModuleRequest.ItemsEntry
	nil,                                    // 46: test.UploadTestModuleRequest.ItemsEntry
	(*timestamp.Timestamp)(nil),            // 47: google.protobuf.Timestamp
}
var file_api_proto_test_test_proto_depIdxs = []int32{
	42, // 0: test.KeyValuePair.values:type_name -> test.KeyValuePair.ValuesEntry
	43, // 1: test.TestModule.itmes:type_name -> test.TestModule.ItmesEntry
	47, // 2: test.TestModule.created_at:type_name -> google.protobuf.Timestamp
	47, // 3: test.TestModule.updated_at:type_name -> google.protobuf.Timestamp
	47, // 4: test.TestModuleApply.created_at:type_name -> google.protobuf.Timestamp
	47, // 5: test.TestRecord.created_at:type_name -> google.protobuf.Timestamp
	47, // 6: test.TestRecord.updated_at:type_name -> google.protobuf.Timestamp
	0,  // 7: test.TestExecution.status:type_name -> test.TestStatus
	47, // 8: test.TestExecution.started_at:type_name -> google.protobuf.Timestamp
	47, // 9: test.TestExecution.finished_at:type_name -> google.protobuf.Timestamp
	47, // 10: test.TestExecution.created_at:type_name -> google.protobuf.Timestamp
	47, // 11: test.TestExecution.updated_at:type_name -> google.protobuf.Timestamp
	44, // 12: test.CreateTestModuleRequest.items:type_name -> test.CreateTestModuleRequest.ItemsEntry
	4,  // 13: test.CreateTestModuleResponse.test_module:type_name -> test.TestModule
	4,  // 14: test.GetTestModuleResponse.test_module:type_name -> test.TestModule
	45, // 15: test.UpdateTestModuleRequest.items:type_name -> test.UpdateTestModuleRequest.ItemsEntry
	4,  // 16: test.UpdateTestModuleResponse.test_module:type_name -> test.TestModule
	4,  // 17: test.ListTestModulesResponse.test_modules:type_name -> test.TestModule
	46, // 18: test.UploadTestModuleRequest.items:type_name -> test.UploadTestModuleRequest.ItemsEntry
	4,  // 19: test.UploadTestModuleResponse.test_module:type_name -> test.TestModule
	5,  // 20: test.ApplyTestModuleResponse.test_module_apply:type_name -> test.TestModuleApply
	5,  // 21: test.ListTestModuleAppliesResponse.test_module_applies:type_name -> test.TestModuleApply
	6,  // 22: test.CreateTestRecordResponse.test_record:type_name -> test.TestRecord
	6,  // 23: test.GetTestRecordResponse.test_record:type_name -> test.TestRecord
	6,  // 24: test.ListTestRecordsResponse.test_records:type_name -> test.TestRecord
	0,  // 25: test.CreateTestExecutionRequest.status:type_name -> test.TestStatus
	7,  // 26: test.CreateTestExecutionResponse.test_execution:type_name -> test.TestExecution
	7,  // 27: test.GetTestExecutionResponse.test_execution:type_name -> test.TestExecution
	7,  // 28: test.ListTestExecutionsResponse.test_executions:type_name -> test.TestExecution
	1,  // 29: test.TestModule.ItmesEntry.value:type_name -> test.KeyValuePair
	1,  // 30: test.CreateTestModuleRequest.ItemsEntry.value:type_name -> test.KeyValuePair
	1,  // 31: test.UpdateTestModuleRequest.ItemsEntry.value:type_name -> test.KeyValuePair
	1,  // 32: test.UploadTestModuleRequest.ItemsEntry.value:type_name -> test.KeyValuePair
	2,  // 33: test.TestService.GetServiceInfo:input_type -> test.GetServiceInfoRequest
	8,  // 34: test.TestService.CreateTestModule:input_type -> test.CreateTestModuleRequest
	10, // 35: test.TestService.GetTestModule:input_type -> test.GetTestModuleRequest
	12, // 36: test.TestService.UpdateTestModule:input_type -> test.UpdateTestModuleRequest
	14, // 37: test.TestService.DeleteTestModule:input_type -> test.DeleteTestModuleRequest
	16, // 38: test.TestService.ListTestModules:input_type -> test.ListTestModulesRequest
	18, // 39: test.TestService.DownloadTestModule:input_type -> test.DownloadTestModuleRequest
	20, // 40: test.TestService.UploadTestModule:input_type -> test.UploadTestModuleRequest
	22, // 41: test.TestService.ApplyTestModule:input_type -> test.ApplyTestModuleRequest
	24, // 42: test.TestService.ListTestModuleApplies:input_type -> test.ListTestModuleAppliesRequest
	26, // 43: test.TestService.ProcessTestModuleApply:input_type -> test.ProcessTestModuleApplyRequest
	28, // 44: test.TestService.CreateTestRecord:input_type -> test.CreateTestRecordRequest
	30, // 45: test.TestService.GetTestRecord:input_type -> test.GetTestRecordRequest
	32, // 46: test.TestService.DeleteTestRecord:input_type -> test.DeleteTestRecordRequest
	34, // 47: test.TestService.ListTestRecords:input_type -> test.ListTestRecordsRequest
	36, // 48: test.TestService.CreateTestExecution:input_type -> test.CreateTestExecutionRequest
	38, // 49: test.TestService.GetTestExecution:input_type -> test.GetTestExecutionRequest
	40, // 50: test.TestService.ListTestExecutions:input_type -> test.ListTestExecutionsRequest
	3,  // 51: test.TestService.GetServiceInfo:output_type -> test.GetServiceInfoResponse
	9,  // 52: test.TestService.CreateTestModule:output_type -> test.CreateTestModuleResponse
	11, // 53: test.TestService.GetTestModule:output_type -> test.GetTestModuleResponse
	13, // 54: test.TestService.UpdateTestModule:output_type -> test.UpdateTestModuleResponse
	15, // 55: test.TestService.DeleteTestModule:output_type -> test.DeleteTestModuleResponse
	17, // 56: test.TestService.ListTestModules:output_type -> test.ListTestModulesResponse
	19, // 57: test.TestService.DownloadTestModule:output_type -> test.DownloadTestModuleResponse
	21, // 58: test.TestService.UploadTestModule:output_type -> test.UploadTestModuleResponse
	23, // 59: test.TestService.ApplyTestModule:output_type -> test.ApplyTestModuleResponse
	25, // 60: test.TestService.ListTestModuleApplies:output_type -> test.ListTestModuleAppliesResponse
	27, // 61: test.TestService.ProcessTestModuleApply:output_type -> test.ProcessTestModuleApplyResponse
	29, // 62: test.TestService.CreateTestRecord:output_type -> test.CreateTestRecordResponse
	31, // 63: test.TestService.GetTestRecord:output_type -> test.GetTestRecordResponse
	33, // 64: test.TestService.DeleteTestRecord:output_type -> test.DeleteTestRecordResponse
	35, // 65: test.TestService.ListTestRecords:output_type -> test.ListTestRecordsResponse
	37, // 66: test.TestService.CreateTestExecution:output_type -> test.CreateTestExecutionResponse
	39, // 67: test.TestService.GetTestExecution:output_type -> test.GetTestExecutionResponse
	41, // 68: test.TestService.ListTestExecutions:output_type -> test.ListTestExecutionsResponse
	51, // [51:69] is the sub-list for method output_type
	33, // [33:51] is the sub-list for method input_type
	33, // [33:33] is the sub-list for extension type_name
	33, // [33:33] is the sub-list for extension extendee
	0,  // [0:33] is the sub-list for field type_name
}

func init() { file_api_proto_test_test_proto_init() }
func file_api_proto_test_test_proto_init() {
	if File_api_proto_test_test_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_proto_test_test_proto_rawDesc), len(file_api_proto_test_test_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   46,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_proto_test_test_proto_goTypes,
		DependencyIndexes: file_api_proto_test_test_proto_depIdxs,
		EnumInfos:         file_api_proto_test_test_proto_enumTypes,
		MessageInfos:      file_api_proto_test_test_proto_msgTypes,
	}.Build()
	File_api_proto_test_test_proto = out.File
	file_api_proto_test_test_proto_goTypes = nil
	file_api_proto_test_test_proto_depIdxs = nil
}
