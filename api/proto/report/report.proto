syntax = "proto3";

package report;

option go_package = "github.com/testplatform/api/proto/report";

import "google/protobuf/timestamp.proto";

// 报告服务定义
service ReportService {
  // 创建报告
  rpc CreateReport(CreateReportRequest) returns (CreateReportResponse);
  // 获取报告
  rpc GetReport(GetReportRequest) returns (GetReportResponse);
  // 报告列表
  rpc ListReports(ListReportsRequest) returns (ListReportsResponse);
  // 删除报告
  rpc DeleteReport(DeleteReportRequest) returns (DeleteReportResponse);
  // 生成测试报告
  rpc GenerateTestReport(GenerateTestReportRequest) returns (GenerateTestReportResponse);
}

// 报告类型
enum ReportType {
  TEST_EXECUTION = 0;
  PERFORMANCE = 1;
  COVERAGE = 2;
  SUMMARY = 3;
}

// 报告格式
enum ReportFormat {
  HTML = 0;
  PDF = 1;
  JSON = 2;
  XML = 3;
}

// 报告
message Report {
  int64 id = 1;
  string title = 2;
  string description = 3;
  ReportType type = 4;
  ReportFormat format = 5;
  string file_path = 6;
  string content = 7;
  int64 created_by = 8;
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;
}

// 测试统计信息
message TestStatistics {
  int64 total_tests = 1;
  int64 passed_tests = 2;
  int64 failed_tests = 3;
  int64 skipped_tests = 4;
  double pass_rate = 5;
  int64 total_duration_ms = 6;
  int64 average_duration_ms = 7;
}

// 创建报告请求
message CreateReportRequest {
  string title = 1;
  string description = 2;
  ReportType type = 3;
  ReportFormat format = 4;
  string content = 5;
  int64 created_by = 6;
}

// 创建报告响应
message CreateReportResponse {
  Report report = 1;
}

// 获取报告请求
message GetReportRequest {
  int64 id = 1;
}

// 获取报告响应
message GetReportResponse {
  Report report = 1;
}

// 报告列表请求
message ListReportsRequest {
  int32 page = 1;
  int32 page_size = 2;
  string search = 3;
  ReportType type = 4;
  int64 created_by = 5;
}

// 报告列表响应
message ListReportsResponse {
  repeated Report reports = 1;
  int64 total = 2;
  int32 page = 3;
  int32 page_size = 4;
}

// 删除报告请求
message DeleteReportRequest {
  int64 id = 1;
}

// 删除报告响应
message DeleteReportResponse {
  bool success = 1;
}

// 生成测试报告请求
message GenerateTestReportRequest {
  repeated int64 test_execution_ids = 1;
  ReportFormat format = 2;
  string title = 3;
  int64 created_by = 4;
}

// 生成测试报告响应
message GenerateTestReportResponse {
  Report report = 1;
  TestStatistics statistics = 2;
}
