version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: testplatform-postgres
    environment:
      POSTGRES_DB: testplatform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./migrations:/docker-entrypoint-initdb.d
    networks:
      - testplatform-network

  # Redis (可选，用于缓存)
  redis:
    image: redis:7-alpine
    container_name: testplatform-redis
    ports:
      - "6379:6379"
    networks:
      - testplatform-network

  # 用户服务
  user-service:
    build:
      context: .
      dockerfile: deployments/user-service/Dockerfile
    container_name: testplatform-user-service
    ports:
      - "50051:50051"
    environment:
      - DATABASE_HOST=postgres
      - DATABASE_PORT=5432
      - DATABASE_USER=postgres
      - DATABASE_PASSWORD=password
      - DATABASE_NAME=testplatform
    depends_on:
      - postgres
    networks:
      - testplatform-network

  # 测试服务
  test-service:
    build:
      context: .
      dockerfile: deployments/test-service/Dockerfile
    container_name: testplatform-test-service
    ports:
      - "50052:50052"
    environment:
      - DATABASE_HOST=postgres
      - DATABASE_PORT=5432
      - DATABASE_USER=postgres
      - DATABASE_PASSWORD=password
      - DATABASE_NAME=testplatform
    depends_on:
      - postgres
    networks:
      - testplatform-network

  # 报告服务
  report-service:
    build:
      context: .
      dockerfile: deployments/report-service/Dockerfile
    container_name: testplatform-report-service
    ports:
      - "50053:50053"
    environment:
      - DATABASE_HOST=postgres
      - DATABASE_PORT=5432
      - DATABASE_USER=postgres
      - DATABASE_PASSWORD=password
      - DATABASE_NAME=testplatform
    depends_on:
      - postgres
    networks:
      - testplatform-network

  # API 网关
  api-gateway:
    build:
      context: .
      dockerfile: deployments/api-gateway/Dockerfile
    container_name: testplatform-api-gateway
    ports:
      - "8080:8080"
    environment:
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=50051
      - TEST_SERVICE_HOST=test-service
      - TEST_SERVICE_PORT=50052
      - REPORT_SERVICE_HOST=report-service
      - REPORT_SERVICE_PORT=50053
    depends_on:
      - user-service
      - test-service
      - report-service
    networks:
      - testplatform-network

volumes:
  postgres_data:

networks:
  testplatform-network:
    driver: bridge
