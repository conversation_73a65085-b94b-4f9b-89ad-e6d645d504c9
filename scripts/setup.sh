#!/bin/bash

# TestPlatform 项目设置脚本

set -e

echo "🚀 Setting up TestPlatform..."

# 检查 Go 版本
echo "📋 Checking Go version..."
if ! command -v go &> /dev/null; then
    echo "❌ Go is not installed. Please install Go 1.21 or later."
    exit 1
fi

GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
REQUIRED_VERSION="1.21"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$GO_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "❌ Go version $GO_VERSION is too old. Please upgrade to Go $REQUIRED_VERSION or later."
    exit 1
fi

echo "✅ Go version $GO_VERSION is compatible."

# 检查 protoc
echo "📋 Checking protoc..."
if ! command -v protoc &> /dev/null; then
    echo "❌ protoc is not installed. Please install Protocol Buffers compiler."
    echo "   Ubuntu/Debian: sudo apt-get install protobuf-compiler"
    echo "   macOS: brew install protobuf"
    exit 1
fi

echo "✅ protoc is installed."

# 安装 Go 依赖
echo "📦 Installing Go dependencies..."
go mod download
go mod tidy

# 安装 protoc-gen-go 和 protoc-gen-go-grpc
echo "📦 Installing protoc plugins..."
go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest

# 生成 protobuf 文件
echo "🔧 Generating protobuf files..."
make proto

# 创建构建目录
echo "📁 Creating build directory..."
mkdir -p build

# 检查 Docker
echo "📋 Checking Docker..."
if command -v docker &> /dev/null; then
    echo "✅ Docker is installed."
    if command -v docker-compose &> /dev/null; then
        echo "✅ Docker Compose is installed."
    else
        echo "⚠️  Docker Compose is not installed. Some features may not work."
    fi
else
    echo "⚠️  Docker is not installed. Some features may not work."
fi

# 检查 PostgreSQL
echo "📋 Checking PostgreSQL..."
if command -v psql &> /dev/null; then
    echo "✅ PostgreSQL client is installed."
else
    echo "⚠️  PostgreSQL client is not installed. You may need it for database operations."
fi

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📚 Next steps:"
echo "   1. Configure your database connection in configs/config.yaml"
echo "   2. Start PostgreSQL database"
echo "   3. Run database migrations: make migrate-up"
echo "   4. Start services: make run-all"
echo ""
echo "🔗 Useful commands:"
echo "   make build          - Build all services"
echo "   make run-gateway    - Run API Gateway"
echo "   make run-user       - Run User Service"
echo "   make run-test       - Run Test Service"
echo "   make run-report     - Run Report Service"
echo "   make docker-up      - Start with Docker Compose"
echo "   make test           - Run tests"
echo ""
