# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Build directory
build/
dist/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Environment variables
.env
.env.local
.env.*.local

# Database
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp/
temp/

# Coverage reports
coverage.txt
coverage.html

# Generated protobuf files (uncomment if you want to ignore them)
# *.pb.go

# Docker volumes
postgres_data/

# Configuration files with sensitive data
configs/config.local.yaml
configs/config.prod.yaml

# Test reports
test-reports/
coverage-reports/

# Binary files
*.tar.gz
*.zip

# Node modules (if using any frontend)
node_modules/

# Python cache (if using any Python tools)
__pycache__/
*.py[cod]
*$py.class
