package config

import (
	"github.com/spf13/viper"
)

// Config 应用配置结构
type Config struct {
	LogLevel      string        `mapstructure:"log_level"`
	Database      Database      `mapstructure:"database"`
	APIGateway    APIGateway    `mapstructure:"api_gateway"`
	UserService   UserService   `mapstructure:"user_service"`
	TestService   TestService   `mapstructure:"test_service"`
	ReportService ReportService `mapstructure:"report_service"`
}

// Database 数据库配置
type Database struct {
	Host      string `mapstructure:"host"`
	Port      string `mapstructure:"port"`
	User      string `mapstructure:"user"`
	Password  string `mapstructure:"password"`
	DBName    string `mapstructure:"dbname"`
	SSLMode   string `mapstructure:"sslmode"`
	IsOnline  bool   `mapstructure:"is_online"`
	CachePath string `mapstructure:"cache_path"`
	LocalPath string `mapstructure:"local_path"`
}

// APIGateway API网关配置
type APIGateway struct {
	Port string `mapstructure:"port"`
}

// UserService 用户服务配置
type UserService struct {
	Port string `mapstructure:"port"`
}

// TestService 测试服务配置
type TestService struct {
	Port string `mapstructure:"port"`
}

// ReportService 报告服务配置
type ReportService struct {
	Port string `mapstructure:"port"`
}

// Load 加载配置
func Load(pathConfig string) (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	if pathConfig != "" {
		viper.AddConfigPath(pathConfig)
	} else {
		viper.AddConfigPath("./configs")
	}
	viper.AddConfigPath(".")

	// 设置默认值
	setDefaults()

	// 读取环境变量
	viper.AutomaticEnv()

	if err := viper.ReadInConfig(); err != nil {
		// 如果配置文件不存在，使用默认值
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, err
		}
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, err
	}

	return &config, nil
}

// setDefaults 设置默认配置值
func setDefaults() {
	viper.SetDefault("log_level", "info")

	// 数据库默认配置
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", "5432")
	viper.SetDefault("database.user", "postgres")
	viper.SetDefault("database.password", "password")
	viper.SetDefault("database.dbname", "testplatform")
	viper.SetDefault("database.sslmode", "disable")

	// 服务端口默认配置
	viper.SetDefault("api_gateway.port", "8080")
	viper.SetDefault("user_service.port", "50051")
	viper.SetDefault("test_service.port", "50052")
	viper.SetDefault("report_service.port", "50053")
}
