package model

import (
	"fmt"
	"time"

	"thinta.test.platform/internal/shared/models"
	DB "thinta.test.platform/pkg/database"
)

// 定义一个全局的当前用户信息
type CurrentUser struct {
	User       *models.User
	Token      string
	ExpireTime time.Time
}

var CurrentUsers = make(map[string]CurrentUser)

// Register 用户注册功能，创建新用户账号。
// @Param username string 用户名
// @Param email string 邮箱地址
// @Param fullName string 用户全名
// @Return error 注册过程中的错误信息
// @Create xinze 2025-06-10
func Register(username string, email string, fullName string) error {
	user := &models.User{
		Username: username,
		Email:    email,
		FullName: fullName,
		Role:     0,
		Status:   0,
	}
	// 检查用户名和邮箱是否已存在
	if err := DB.PlatformDB.Where("username = ?", username).First(user).Error; err == nil {
		return fmt.Errorf("username already exists")
	}
	if err := DB.PlatformDB.Where("email = ?", email).First(user).Error; err == nil {
		return fmt.Errorf("email already exists")
	}

	// 保存用户信息到数据库
	user.Password = "11111111"
	DB.PlatformDB.Create(&user)
	return nil
}

// Login 用户登录功能，验证用户身份并生成登录令牌。
// @Param username string 用户名
// @Param password string 用户密码
// @Return *CurrentUser 当前用户信息
// @Return error 登录过程中的错误信息
// @Create xinze 2025-06-10
func Login(username, password string) (*CurrentUser, error) {
	user := &models.User{}
	// 查询用户信息
	if err := DB.PlatformDB.Where("username = ?", username).First(user).Error; err != nil {
		return nil, err
	}
	// 验证用户状态是否正常
	if user.Status != 1 {
		return nil, fmt.Errorf("user not active")
	}
	// 验证密码
	if user.Password != password {
		return nil, fmt.Errorf("password not match")
	}
	// 生成 token
	token := fmt.Sprintf("%d_%d", user.ID, time.Now().Unix())
	currentUser := CurrentUser{
		User:       user,
		Token:      token,
		ExpireTime: time.Now().Add(24 * time.Hour),
	}
	CurrentUsers[token] = currentUser

	return &currentUser, nil
}

// Logout 用户登出功能，清除用户登录状态。
// @Param token string 用户登录令牌
// @Return error 登出过程中的错误信息
// @Create xinze 2025-06-10
func Logout(token string) error {
	// 清空本地缓存的用户数据
	DB.PlatformDB.Delete(&models.LocalUser{})
	delete(CurrentUsers, token)
	return nil
}

func chekcToken(token string) (*models.User, error) {
	// 根据 token 判断用户是否登录
	currentUser, ok := CurrentUsers[token]
	if !ok {
		return nil, fmt.Errorf("not login")
	}
	if currentUser.ExpireTime.Before(time.Now()) {
		return nil, fmt.Errorf("token expired")
	}
	return currentUser.User, nil
}

// GetUsers 获取用户列表功能，支持分页查询。
// @Param token string 用户登录令牌
// @Param page int 页码
// @Param pageSize int 每页数量
// @Return []models.User 用户列表
// @Return error 查询过程中的错误信息
// @Create xinze 2025-06-10
func GetUsers(token string, page int, pageSize int) ([]models.User, error) {
	user, err := chekcToken(token)
	if err != nil {
		return nil, err
	}

	// 合规性判断（仅平台管理员及用户管理员可查询）
	if user.Role != 1 && user.Role != 5 {
		return nil, fmt.Errorf("permission denied")
	}
	var users []models.User
	if err := DB.PlatformDB.Limit(pageSize).Offset((page - 1) * pageSize).Find(&users).Error; err != nil {
		return nil, err
	}
	return users, nil
}

// GetUserByCondition 根据条件查询用户信息
// @Param token string 用户登录令牌
// @Param page int 页码
// @Param pageSize int 每页数量
// @Return []models.User 用户列表
// @Return error 查询过程中的错误信息
// @Create xinze 2025-06-10
func GetUserByCondition(token string, page int, pageSize int, condition string) ([]models.User, error) {
	user, err := chekcToken(token)
	if err != nil {
		return nil, err
	}
	// 合规性判断（仅平台管理员及用户管理员可查询）
	if user.Role != 1 && user.Role != 5 {
		return nil, fmt.Errorf("permission denied")
	}
	var users []models.User
	if err := DB.PlatformDB.Where(condition).Limit(pageSize).Offset((page - 1) * pageSize).Find(&users).Error; err != nil {
		return nil, err
	}
	// if err := DB.PlatformDB.Where(condition).Find(&users).Error; err != nil {
	// 	return nil, err
	// }
	return users, nil
}

// UpdateUser 更新用户数据
// @Param token string 用户登录令牌
// @Param user *models.User 用户数据
// @Return *models.User 更新后的用户数据
// @Return error 更新过程中的错误信息
// @Create xinze 2025-06-10
func UpdateUser(token string, user *models.User) (*models.User, error) {
	userCache, err := chekcToken(token)
	if err != nil {
		return nil, err
	}

	// 更新自己的账号数据
	if userCache.ID == user.ID {
		userCache.Username = user.Username
		userCache.Email = user.Email
		userCache.FullName = user.FullName
		if err := DB.PlatformDB.Save(userCache).Error; err != nil {
			return nil, err
		}
		return userCache, nil
	}

	// 查询用户信息
	tmp := &models.User{}
	if err := DB.PlatformDB.Where("id = ?", user.ID).First(tmp).Error; err != nil {
		return nil, err
	}

	// 判断是不是更新自己账户信息
	if userCache.Role == 1 { // 判断是否为管理员更新用户数据
		if tmp.Role == 1 {
			return nil, fmt.Errorf("can not update admin user")
		}
		tmp.Username = user.Username
		tmp.Email = user.Email
		tmp.FullName = user.FullName
		tmp.Role = user.Role
		tmp.Status = user.Status
	} else if userCache.Role == 5 { // 判断是否为用户管理员更新用户数据
		if user.Role == 1 || user.Role == 5 {
			return nil, fmt.Errorf("can not update admin user")
		}
		tmp.Username = user.Username
		tmp.Email = user.Email
		tmp.FullName = user.FullName
		tmp.Role = user.Role
		tmp.Status = user.Status
	} else {
		return nil, fmt.Errorf("permission denied")
	}

	// 更新用户数据
	if err := DB.PlatformDB.Save(tmp).Error; err != nil {
		return nil, err
	}
	return tmp, nil
}

// UpdateUserByParam 更新用户数据
// @Param token string 用户登录令牌
// @Param id int64 用户ID
// @Param username string 用户名
// @Param email string 邮箱
// @Param fullName string 用户全名
// @Param role int32 用户角色
// @Param status int32 用户状态
// @Param testModules []string 用户可访问的测试模块
// @Return *models.User 更新后的用户数据
// @Return error 更新过程中的错误信息
// @Create xinze 2025-06-10
func UpdateUserByParam(token string, id int64, username string, email string, fullName string, role int32, status int32, testModules []string) (*models.User, error) {
	userCache, err := chekcToken(token)
	if err != nil {
		return nil, err
	}

	// 更新自己的账号数据
	if userCache.ID == id {
		// 普通用户只能修改自己的基本信息，不能修改角色和状态
		if role != -1 || status != -1 {
			return nil, fmt.Errorf("permission denied: cannot change role or status")
		}
		if username != "" {
			userCache.Username = username
		}
		if email != "" {
			userCache.Email = email
		}
		if fullName != "" {
			userCache.FullName = fullName
		}
		if err := DB.PlatformDB.Save(userCache).Error; err != nil {
			return nil, err
		}
		return userCache, nil
	}

	// 查询用户信息
	tmp := &models.User{}
	if err := DB.PlatformDB.Where("id = ?", id).First(tmp).Error; err != nil {
		return nil, err
	}

	// 判断是不是更新自己账户信息
	if userCache.Role == 1 { // 判断是否为管理员更新用户数据
		if tmp.Role == 1 {
			return nil, fmt.Errorf("can not update admin user")
		}
		if username != "" {
			tmp.Username = username
		}
		if email != "" {
			tmp.Email = email
		}
		if fullName != "" {
			tmp.FullName = fullName
		}
		if role != -1 {
			tmp.Role = role
		}
		if status != -1 {
			tmp.Status = status
		}
		if testModules != nil {
			tmp.TestModules = testModules
		}
	} else if userCache.Role == 5 { // 判断是否为用户管理员更新用户数据
		if role == 1 || role == 5 {
			return nil, fmt.Errorf("can not update admin user")
		}
		if username != "" {
			tmp.Username = username
		}
		if email != "" {
			tmp.Email = email
		}
		if fullName != "" {
			tmp.FullName = fullName
		}
		if role != -1 {
			tmp.Role = role
		}
		if status != -1 {
			tmp.Status = status
		}
	} else {
		return nil, fmt.Errorf("permission denied")
	}

	// 更新用户数据
	if err := DB.PlatformDB.Save(tmp).Error; err != nil {
		return nil, err
	}
	return tmp, nil
}

// ChangeUserPassword 修改用户密码
// @Param token string 用户登录令牌
// @Param username string 用户名
// @Param oldPassword string 旧密码
// @Param newPassword string 新密码
// @Return error 修改过程中的错误信息
// @Create xinze 2025-06-10
func ChangeUserPassword(token string, username string, oldPassword, newPassword string) error {
	user, err := chekcToken(token)
	if err != nil {
		return nil
	}
	tmp := &models.User{}
	if err := DB.PlatformDB.Where("username = ?", username).First(tmp).Error; err != nil {
		return err
	}
	// 判断是否有权限修改密码
	if user.ID != tmp.ID && user.Role != 1 && user.Role != 5 {
		return fmt.Errorf("can not change user password, permission denied")
	}
	if tmp.Password != oldPassword {
		return fmt.Errorf("old password not match")
	}
	// 更新密码内容
	tmp.Password = newPassword
	if err := DB.PlatformDB.Save(tmp).Error; err != nil {
		return err
	}
	return nil
}

// DeleteUser 删除用户
// @Param token string 用户登录令牌
// @Param username string 用户名
// @Return error 删除过程中的错误信息
// @Create xinze 2025-06-10
func DeleteUser(token string, username string) error {
	user, err := chekcToken(token)
	if err != nil {
		return err
	}
	tmp := &models.User{}
	if err := DB.PlatformDB.Where("username = ?", username).First(tmp).Error; err != nil {
		return err
	}
	// 判断是否为删除管理员账号（管理员账号不可删除）
	if tmp.Role == 1 {
		return fmt.Errorf("can not delete admin user")
	}
	// 判断是否为当前用户删除自己
	if user.ID == tmp.ID {
		return fmt.Errorf("can not delete self")
	}
	if user.Role != 1 && user.Role != 5 {
		return fmt.Errorf("permission denied")
	}

	// 删除用户
	if err := DB.PlatformDB.Delete(tmp).Error; err != nil {
		return err
	}
	return nil
}

// AuthenticateUser 用户认证
// @Param token string 用户登录令牌
// @Param password string 用户密码
// @Return error 认证过程中的错误信息
// @Create xinze 2025-06-10
func AuthenticateUser(token string, password string) error {
	user, err := chekcToken(token)
	if err != nil {
		return err
	}
	// 判断密码是否正确
	if user.Password != password {
		return fmt.Errorf("password not match")
	}
	return nil
}

// ResetUserPassword 重置用户密码
// @Param token string 用户登录令牌
// @Param username string 用户名
// @Return error 重置过程中的错误信息
// @Create xinze 2025-06-10
func ResetUserPassword(token string, username string) error {
	user, err := chekcToken(token)
	if err != nil {
		return err
	}
	tmp := &models.User{}
	if err := DB.PlatformDB.Where("username = ?", username).First(tmp).Error; err != nil {
		return err
	}
	// 判断是否为管理员重置密码
	if user.Role != 1 && user.Role != 5 {
		return fmt.Errorf("permission denied")
	}
	// 重置密码
	tmp.Password = "11111111"
	if err := DB.PlatformDB.Save(tmp).Error; err != nil {
		return err
	}
	return nil
}

/* ********************************************************************************************************** *
 * 以下代码为本地用户数据操作方法                                                                                *
 * ********************************************************************************************************** */

// UpdateLocalUser 更新本地缓存的用户数据
// @Param user *models.LocalUser 用户数据
// @Return error 更新过程中的错误信息
// @Create xinze 2025-06-10
func UpdateLocalUser(user *models.LocalUser) error {
	// 删除 localuser 表内所有数据
	DB.PlatformDB.Delete(&models.LocalUser{})
	// 插入新数据
	DB.PlatformDB.Create(&models.LocalUser{
		Username: user.Username,
		Email:    user.Email,
		FullName: user.FullName,
		Role:     user.Role,
	})
	return nil
}

// DeleteLocalUser 删除本地缓存的用户数据
// @Return error 删除过程中的错误信息
// @Create xinze 2025-06-10
func DeleteLocalUser() error {
	// 删除 localuser 表内所有数据
	DB.PlatformDB.Delete(&models.LocalUser{})
	return nil
}

// 简单检查一下本地账号使用时间
// func CheckLocalUseTime() error {
// 	// 查询 localuser 表内所有数据
// 	DB.PlatformDB.First(LocalUser)
// 	// 当前时间大于创建时间 7 天，限制本地账号使用
// 	if LocalUser.CreatedAt.Before(time.Now().Add(-7 * 24 * time.Hour)) {
// 		return fmt.Errorf("local user expired")
// 	}
// 	return nil
// }
