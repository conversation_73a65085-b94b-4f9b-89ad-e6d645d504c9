package database

// type postgresDB *gorm.DB
// type sqliteDB *gorm.DB

// // 全局 PostgreSQL 数据库连接变量
// // var postgresDB *gorm.DB

// // 全局 SQLite 数据库连接变量
// // var sqliteDB *gorm.DB
// var sqliteDBObj *gorm.DB
// var
// var PlatformDB *platformDB

// type platformDB struct {
// 	Database *gorm.DB
// 	IsOnline bool
// }

// func NewPlatformDB(db *gorm.DB, isOnline bool) *platformDB {
// 	if isOnline {
// 		// 初始化在线数据库
// 		PlatformDB = db
// 	} else {
// 		sqliteDB = db
// 	}

// 	return &PlatformDB{
// 		Database: db,
// 		IsOnline: isOnline,
// 	}
// }
