package database

import (
	"testing"

	"thinta.test.platform/pkg/config"
	"thinta.test.platform/pkg/logger"
)

func TestNewPostgresDB(t *testing.T) {
	// 初始化日志
	logger.Init("debug")

	// 加载配置
	cfg, err := config.Load("../../configs")
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	// 创建数据库连接
	err = NewPlatformDB(cfg.Database)
	if err != nil {
		t.Fatalf("Failed to create database connection: %v", err)
	}

	// 关闭数据库连接
	if err := Close(); err != nil {
		t.Fatalf("Failed to close database connection: %v", err)
	}
}
