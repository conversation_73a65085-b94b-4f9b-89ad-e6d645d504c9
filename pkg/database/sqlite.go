package database

import (
	"fmt"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"thinta.test.platform/internal/shared/models"
	"thinta.test.platform/pkg/config"
)

var LocalDB *gorm.DB = nil

func NewSQLiteDB(cfg config.Database) error {
	path := cfg.LocalPath
	if path == "" {
		path = "./.local/database/platform.db"
	} else {
		path = path + "/database/platform.db"
	}
	db, err := gorm.Open(sqlite.Open(path), &gorm.Config{
		// SkipDefaultTransaction: true,
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	LocalDB = db

	// 初始化本地用户存储
	if err := initLocalUser(); err != nil {
		return fmt.Errorf("failed to init local user: %w", err)
	}
	return nil
}

func AutoMigrateSQLite(models ...interface{}) error {
	// 遍历所有模型并执行迁移
	for _, model := range models {
		if err := LocalDB.AutoMigrate(model); err != nil {
			return fmt.Errorf("failed to migrate model %T: %w", model, err)
		}
	}
	return nil
}

func initLocalUser() error {
	// 检查 local_user 表是否存在
	var count int64
	LocalDB.Table("local_user").Count(&count)
	if count == 0 {
		// 如果不存在，则创建表
		err := AutoMigrateSQLite(LocalDB, &models.LocalUser{})
		if err != nil {
			return err
		}
	}
	return nil
}
