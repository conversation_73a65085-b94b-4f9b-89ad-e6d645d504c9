package database

import (
	"fmt"

	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"thinta.test.platform/internal/shared/models"
	"thinta.test.platform/pkg/config"
)

var PlatformDB *gorm.DB = nil

// NewPlatformDB 创建 PostgreSQL 数据库连接
func NewPlatformDB(cfg config.Database) error {
	var err error = nil
	if cfg.IsOnline {
		// 初始化在线数据库
		err = newOnlineDB(cfg)
	} else {
		// 初始化本地数据库
		err = newLocalDB(cfg.CachePath)
	}
	if err != nil {
		return err
	}

	// 初始化数据库
	if err := initDB(); err != nil {
		return fmt.Errorf("failed to init database: %w", err)
	}

	// 获取底层的 sql.DB 对象来配置连接池
	sqlDB, err := PlatformDB.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	return nil
}

// 重置数据库连接，主要切换数据库的离线在线状态
func ResetConnection(cfg config.Database, isOnline bool) error {
	// 关闭数据库连接
	if err := Close(); err != nil {
		return err
	}

	// 更新配置
	cfg.IsOnline = isOnline

	// 重新创建数据库连接
	return NewPlatformDB(cfg)
}

// 初始化在线数据库
func newOnlineDB(cfg config.Database) error {
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=%s client_encoding=UTF8",
		cfg.Host, cfg.User, cfg.Password, cfg.DBName, cfg.Port, cfg.SSLMode)
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		// SkipDefaultTransaction: true,
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	PlatformDB = db

	if err := initDB(); err != nil {
		return fmt.Errorf("failed to init database: %w", err)
	}

	return nil
}

// 初始化本地数据库
func newLocalDB(path string) error {
	if path == "" {
		path = "./.cache/database/platform.db"
	} else {
		path = path + "/database/platform.db"
	}
	db, err := gorm.Open(sqlite.Open(path), &gorm.Config{
		// SkipDefaultTransaction: true,
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	PlatformDB = db
	return nil
}

// AutoMigrate 自动迁移数据库表结构
func AutoMigrate(db *gorm.DB, models ...interface{}) error {
	return db.AutoMigrate(models...)
}

func Close() error {
	sqlDB, err := PlatformDB.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}
	return sqlDB.Close()
}

// 数据库初始化
func initDB() error {
	// 初始化 users 表
	err := initUsers()
	if err != nil {
		return err
	}
	err = initTests()
	if err != nil {
		return err
	}
	return nil
}

func initUsers() error {
	// 检查 users 表是否存在
	if !PlatformDB.Migrator().HasTable("users") {
		// 如果不存在，则创建表
		err := AutoMigrate(PlatformDB, &models.User{})
		if err != nil {
			return err
		}
		// 创建默认管理员用户
		PlatformDB.Create(&models.User{
			Username: "admin",
			Email:    "<EMAIL>",
			Password: "admin",
			FullName: "Administrator",
			Role:     1,
			Status:   1,
		})
	}
	return nil
}

func initTests() error {
	err := initTestModules()
	if err != nil {
		return err
	}
	err = initTestModuleApplies()
	if err != nil {
		return err
	}
	err = initTestRecords()
	if err != nil {
		return err
	}
	err = initTestExecutions()
	if err != nil {
		return err
	}
	return nil
}

func initTestModules() error {
	// 检查 test_modules 表是否存在
	if !PlatformDB.Migrator().HasTable("test_modules") {
		// 如果不存在，则创建表
		err := AutoMigrate(PlatformDB, &models.TestModule{})
		if err != nil {
			return err
		}
	}
	return nil
}

func initTestModuleApplies() error {
	// 检查 test_module_requests 表是否存在
	if !PlatformDB.Migrator().HasTable("test_module_requests") {
		// 如果不存在，则创建表
		err := AutoMigrate(PlatformDB, &models.TestModuleApply{})
		if err != nil {
			return err
		}
	}
	return nil
}

func initTestRecords() error {
	// 检查 test_records 表是否存在
	if !PlatformDB.Migrator().HasTable("test_records") {
		// 如果不存在，则创建表
		err := AutoMigrate(PlatformDB, &models.TestRecord{})
		if err != nil {
			return err
		}
	}
	return nil
}

func initTestExecutions() error {
	// 检查 test_executions 表是否存在
	if !PlatformDB.Migrator().HasTable("test_executions") {
		// 如果不存在，则创建表
		err := AutoMigrate(PlatformDB, &models.TestExecution{})
		if err != nil {
			return err
		}
	}
	return nil
}
